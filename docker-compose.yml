version: '3.8'

services:
  # Foxy AI Service Application
  foxy-ai-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: foxy-ai-service
    restart: unless-stopped
    environment:
      # MongoDB Configuration
      MONGODB_URI: mongodb://mongodb:27017/
      MONGODB_DATABASE: foxy_ai
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_USERNAME: default
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
      
      # Application Configuration
      PYTHONPATH: /app
      LOG_LEVEL: INFO
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    depends_on:
      - mongodb
      - redis
    networks:
      - foxy-network
    # Uncomment if you want to expose a port for future web interface
    # ports:
    #   - "8000:8000"

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: foxy-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      MONGO_INITDB_DATABASE: foxy_ai
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    ports:
      - "27017:27017"
    networks:
      - foxy-network

  # Redis Cache/Queue
  redis:
    image: redis:7.2-alpine
    container_name: foxy-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - foxy-network

  # Redis Commander (Optional - Web UI for Redis)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: foxy-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-}
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - foxy-network
    profiles:
      - tools

  # Mongo Express (Optional - Web UI for MongoDB)
  mongo-express:
    image: mongo-express:latest
    container_name: foxy-mongo-express
    restart: unless-stopped
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: ${MONGO_ROOT_USERNAME:-admin}
      ME_CONFIG_MONGODB_ADMINPASSWORD: ${MONGO_ROOT_PASSWORD:-password}
      ME_CONFIG_MONGODB_URL: **************************************/
      ME_CONFIG_BASICAUTH_USERNAME: ${MONGO_EXPRESS_USERNAME:-admin}
      ME_CONFIG_BASICAUTH_PASSWORD: ${MONGO_EXPRESS_PASSWORD:-pass}
    ports:
      - "8082:8081"
    depends_on:
      - mongodb
    networks:
      - foxy-network
    profiles:
      - tools

volumes:
  mongodb_data:
    driver: local
  redis_data:
    driver: local

networks:
  foxy-network:
    driver: bridge
