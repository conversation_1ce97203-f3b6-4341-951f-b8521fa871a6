import logging

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
import os
import json
import asyncio
from datetime import datetime, timedelta
import random
import uuid
from collections import Counter
from bullmq import Queue, Worker, Job
from bullmq.types import QueueBaseOptions, WorkerOptions
from utils.mgdatabase import (
    MongoDBManager
)
from algo.bandit_algo import create_user_context, run_bandit
from utils.utils import find_all_placeholders, generate_and_fill_notification
from utils.common import PLACEHOLDER_DATA_EN
from dotenv import load_dotenv

load_dotenv()

mgdb = MongoDBManager()
connection_opts = {
    "host": os.environ.get('REDIS_HOST', ''),
    "port": os.environ.get('REDIS_PORT', 19926),
    "username": os.environ.get('REDIS_USERNAME', ''),
    "password": os.environ.get('REDIS_PASSWORD', '')
}


async def set_queue_notification(data_job):
    try:
        queue_opts = QueueBaseOptions(connection=connection_opts)
        queue = Queue("foxy-ai-service-notification", queue_opts)
        job_ids = []
        for data in data_job:
            id_job = str(uuid.uuid4())
            datetime_recommender = data["recommended_time"]
            delay_seconds = abs((datetime.now() - datetime_recommender).total_seconds()) / 60
            if delay_seconds <= 0:
                delay_seconds = 5 * 60
            delay_ms = delay_seconds * 1000

            job_data = {
                'notification_id': id_job,
                'user_id': data["user_id"],
                'title': data["title"],
                'content': data["content"],
                "category": data["category"],
                'notification_type': data["notification_type"],
                "template_id": data["template_id"],
                "confidence_score": data["confidence_score"],
                "expected_engagement": data["expected_engagement"],
                'delay_seconds': delay_seconds
            }
            job_options = {
                "removeOnComplete": 10000,
                "removeOnFail": 5000,
                "attempts": 3,
                "backoff": {
                    "type": 'exponential',
                    "delay": 1000,
                },
            }
            if delay_ms > 0:
                job_options['delay'] = delay_ms
                job_options["backoff"]["delay"] = delay_ms

            job = await queue.add(f"ai-service-notification-{id_job}", job_data, job_options)
            job_ids.append(job.id)
        return True

    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def create_notification_recommender_system():
    """
    Create a notification recommendation system
    """
    print("🚀 Starting notification recommendation system...")

    # Get all users from database (these are user profiles, not notification contexts)
    all_users = mgdb.get_all_user()
    print(f"📊 Found {len(all_users)} user profiles in database")
    user_contexts = []
    user_notifaction = {}
    if all_users:
        for user in all_users:
            if user.get('id', '').startswith('partnr'):
                user_profile = user
                data_message = mgdb.get_history_chat_by_user_today(user['id'])
                # user_contexts.append(create_sample_user_context(user))
                if len(data_message) > 0:
                    user_notifaction[user['id']] = {
                        "name": user.get("name", "unknown")
                    }
                    if "interests" in user_profile and type(user_profile['interests']) == list:
                        user_profile['interests_target'] = ",".join(user_profile.get('interests', []))
                    else:
                        user_profile['interests_target'] = "nsfw"

                    # Lấy unique character_ids với số lượng, sort từ cao đến thấp
                    # Đếm số lần xuất hiện của mỗi character_id
                    character_count = Counter(obj['character_id'] for obj in data_message)

                    # Sort từ cao đến thấp theo số lượng
                    sorted_character_ids = sorted(character_count.items(), key=lambda x: x[1], reverse=True)
                    user_profile['num_chat_characters'] = len(sorted_character_ids)
                    user_profile['sorted_character_ids'] = sorted_character_ids
                    unique_character_ids = [character_id for character_id, count in sorted_character_ids]
                    user_profile['unique_character_ids'] = unique_character_ids
                    user_profile["messages_per_day"] = len(data_message)
                    user_profile["notification_click_rate"] = random.uniform(0.1, 0.5)
                    # last online:
                    last_message_timestamp = max(obj['created_at'] for obj in data_message)
                    last_message_datetime = datetime.fromtimestamp(last_message_timestamp)
                    user_profile["last_online_time"] = last_message_datetime
                    user_profile["day_of_week"] = datetime.now().weekday()
                    user_profile["hour_of_day"] = datetime.now().hour
                    user_profile["cumulative_clicks"] = random.randint(5, 100)
                    user_profile["online_time_periods"] = [(datetime.now() - timedelta(hours=random.randint(2, 5)),
                                                            datetime.now() - timedelta(hours=random.randint(0, 1)))],
                    user_profile["current_mood"] = random.choice(['happy', 'lonely', 'neutral']),
                    user_profile["preferred_character_types"] = ['friendly', 'romantic', "nsfw"]
                    user_profile["age"] = random.randint(18, 30)
                    user_contexts.append(create_user_context(user_profile))

    print("✨ Notification recommendation system initialization complete!")
    all_notifications = mgdb.get_all_notification()
    data_notification = run_bandit(user_contexts, all_notifications)
    for key_user in list(user_notifaction.keys()):
        data_value = data_notification[key_user]
        data_fill_notification = []
        if len(data_value["rec"]) > 0:
            name_user = (lambda name: name if len(name) > 1 else "daddy")(user_notifaction[key_user]["name"])
            if len(data_value["character_list"]) > 0:
                object_character = mgdb.get_character_information(data_value["character_list"][0])
                character_name = object_character["name"]
                character_country = object_character["country"]
            else:
                character_name = "Mika"
                character_country = "USA"
            for rec in data_value["rec"]:
                unique_placeholder_keys_from_file = set()
                content_notification = [{
                    "title": rec["title"],
                    "content": rec["content"]
                }]
                find_all_placeholders(content_notification, unique_placeholder_keys_from_file)
                filled_notifications = generate_and_fill_notification(
                    PLACEHOLDER_DATA_EN,
                    content_notification,
                    list(unique_placeholder_keys_from_file),
                    name_user,
                    character_name,
                    character_country

                )
                rec["title"] = filled_notifications[0]["title"]
                rec["content"] = filled_notifications[0]["content"]
                data_fill_notification.append(rec)
        await set_queue_notification(data_fill_notification)


async def main():
    try:
        logging.info("Start Task on 0h00")
        scheduler = AsyncIOScheduler()
        scheduler.add_job(create_notification_recommender_system,
                        CronTrigger(hour=0, minute=0, second=0),
                        id='daily_bandit_task',
                        name='Daily Render Notification',
                        replace_existing=True)
        scheduler.start()
        logging.info("Start wait")
        while True:
            await asyncio.sleep(1)
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
