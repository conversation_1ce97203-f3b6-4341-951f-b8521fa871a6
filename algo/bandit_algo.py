import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, field
import json
from collections import defaultdict
import random
import re
from pathlib import Path
import logging # Import the logging module

# --- Logging Setup ---
def setup_logging(level=logging.DEBUG):
    """Configures basic logging."""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

# --- Constants for Categories ---
CAT_ENGAGEMENT = "engagement"
CAT_SOCIAL = "social"
CAT_MONETIZATION = "monetization"
CAT_RETENTION = "retention"
CAT_ACTIVITY = "activity"
CAT_ROMANTIC = "romantic"
CAT_PREMIUM = "premium"
CAT_NSFW = "nsfw"
CAT_COMFORT = "comfort"
CAT_INTERACTIVE = "interactive"
CAT_GRATITUDE = "gratitude"
CAT_MILESTONE = "milestone"

# A list of known categories for documentation or sample data generation.
KNOWN_NOTIFICATION_CATEGORIES = [
    CAT_ENGAGEMENT, CAT_SOCIAL, CAT_MONETIZATION, CAT_RETENTION,
    CAT_ACTIVITY, CAT_ROMANTIC, CAT_PREMIUM, CAT_NSFW, CAT_COMFORT,
    CAT_INTERACTIVE, CAT_GRATITUDE, CAT_MILESTONE
]

# --- Dataclasses ---
@dataclass
class NotificationTemplate:
    notification_id: str; title: str; content: str; notification_type: str
    category: str; nsfw_level: int; engagement_score: float
    user_context_requirements: Dict[str, Any]; placeholder_variables: List[str]

@dataclass
class UserNotificationPreferences:
    user_id: str; preferred_hours: List[int]; preferred_days: List[int]
    max_notifications_per_day: int; time_zone_offset: int
    do_not_disturb_start: int; do_not_disturb_end: int
    notification_frequency: str; nsfw_tolerance: int
    preferred_categories: List[str] = field(default_factory=list)
    blocked_categories: List[str] = field(default_factory=list)

@dataclass
class UserContext:
    user_id: str; date: datetime; subscription_status: str; interests_target: str
    gender: str; age: int; gifts_given: int; diamonds_spent: float
    num_chat_characters: int; messages_per_day: float; notification_click_rate: float
    recent_chat_characters: List[str]; top_chat_characters: List[str]
    online_time_periods: List[Tuple[datetime, datetime]]; last_online_time: datetime
    day_of_week: int; hour_of_day: int; cumulative_clicks: int; cumulative_notifications: int
    recent_activity_level: float; preferred_character_types: List[str]; current_mood: str
    notification_preferences: UserNotificationPreferences

# --- Classes with Logging ---

class NotificationContentManager:
    def __init__(self, notification_json_data: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Khởi tạo ContentManager")
        self.templates: Dict[str, NotificationTemplate] = {}
        self.category_mapping: Dict[str, List[str]] = defaultdict(list)
        self.all_loaded_categories: set[str] = set()
        try:
            self.load_templates(notification_json_data)
            self.setup_category_mappings()
            self.logger.info(f"ContentManager khởi tạo hoàn tất, đã tải {len(self.templates)} mẫu.")
        except Exception as e:
            self.logger.critical(f"LỖI NGHIÊM TRỌNG khi khởi tạo ContentManager: {e}", exc_info=True)
            raise

    def load_templates(self, notification_json_data: Optional[Dict[str, Any]]):
        
        for i, notification_data in enumerate(notification_json_data):
            notif_id_from_json = notification_data.get('notification_id', notification_data.get('id'))
            template_id = notif_id_from_json if notif_id_from_json else f"notif_data_{i:04d}"
            try:
                template = self.create_template_from_json(notification_data, template_id)
                self.templates[template.notification_id] = template
                self.all_loaded_categories.add(template.category)
            except Exception as e:
                self.logger.error(f"Lỗi khi tạo mẫu từ dữ liệu (id: {template_id}, index: {i}): {notification_data}. Lỗi: {e}", exc_info=True)
        self.logger.debug(f"Hoàn thành tải mẫu, tổng số mẫu đã xử lý: {len(self.templates)}")

    def create_template_from_json(self, notification_data: Dict, template_id: str) -> NotificationTemplate:
        notification_type = notification_data.get('type', 'Chưa rõ')
        title = notification_data.get('title', '')
        content = notification_data.get('content', '')
        explicit_category = notification_data.get('category')
        if explicit_category and isinstance(explicit_category, str) and explicit_category.strip():
            category = explicit_category.lower().strip().replace(" ", "_")
        else:
            category = self.determine_category_from_fields(notification_type, title, content)
        nsfw_level = self.determine_nsfw_level(notification_type, title, content)
        placeholders = self.extract_placeholders(title + ' ' + content)
        engagement_score = self.calculate_engagement_score(notification_type, content)
        context_requirements = self.determine_context_requirements(notification_type, placeholders)
        return NotificationTemplate(
            notification_id=template_id, title=title, content=content, notification_type=notification_type,
            category=category, nsfw_level=nsfw_level, engagement_score=engagement_score,
            user_context_requirements=context_requirements, placeholder_variables=placeholders
        )

    def determine_category_from_fields(self, notification_type: str, title: str, content: str) -> str:
        type_lower = notification_type.lower(); title_lower = title.lower(); content_lower = content.lower()
        if any(kw in type_lower for kw in ['nsfw', 'explicit', 'sexual', 'kinky']): return CAT_NSFW
        if any(kw in content_lower for kw in ['cock', 'pussy', 'dick', 'cum', 'fuck', 'ass', 'tits', 'horny', 'wet', 'hard']): return CAT_NSFW
        if any(kw in type_lower for kw in ['comfort', 'cute', 'sweet', 'care', 'support']): return CAT_COMFORT
        if any(kw in type_lower for kw in ['match', 'like', 'message', 'view', 'chat', 'friend', 'connect']): return CAT_SOCIAL
        if any(kw in type_lower for kw in ['premium', 'offer', 'boost', 'upgrade', 'vip', 'discount', 'sale', 'coin', 'gem']): return CAT_MONETIZATION
        if any(kw in type_lower for kw in ['miss', 'inactive', 'reminder', 'comeback', 'return', 'away']): return CAT_RETENTION
        if any(kw in type_lower for kw in ['interactive', 'poll', 'dare', 'question', 'challenge', 'quiz', 'game']): return CAT_INTERACTIVE
        if any(kw in type_lower for kw in ['gratitude', 'thanks', 'appreciate', 'kudos']): return CAT_GRATITUDE
        if any(kw in type_lower for kw in ['milestone', 'achievement', 'streak', 'anniversary', 'record']): return CAT_MILESTONE
        if any(kw in type_lower for kw in ['activity', 'online', 'nearby', 'live', 'update']): return CAT_ACTIVITY
        if any(kw in type_lower for kw in ['new', 'alert', 'check out']): return CAT_ENGAGEMENT
        if type_lower in KNOWN_NOTIFICATION_CATEGORIES: return type_lower
        return CAT_ROMANTIC # Default fallback

    def determine_nsfw_level(self, nt: str, title: str, content: str) -> int:
        txt = (nt + ' ' + title + ' ' + content).lower()
        if any(kw in txt for kw in ['cock', 'pussy', 'dick', 'cum', 'fuck', 'ass', 'tits', 'horny', 'wet', 'hard', 'suck', 'lick', 'ride', 'pound', 'thrust', 'penetrate', 'orgasm', 'climax']): return 3
        if any(kw in txt for kw in ['sexy', 'hot', 'desire', 'passion', 'intimate', 'sensual', 'seductive', 'naughty', 'dirty', 'kiss', 'touch', 'caress', 'lust', 'arouse']): return 2
        if any(kw in txt for kw in ['attractive', 'cute', 'beautiful', 'handsome', 'flirt', 'charm', 'romantic', 'date', 'love', 'crush', 'sweetheart', 'darling']): return 1
        return 0

    def extract_placeholders(self, text: str) -> List[str]: return list(set(re.findall(r'\{([^}]+)\}', text)))
    def calculate_engagement_score(self, nt: str, content: str) -> float:
        bs = 0.3; type_l = nt.lower()
        if any(ht.lower() in type_l for ht in ['New Match', 'Message Received', 'Super Like Received', 'Gift Received']): bs = 0.7
        elif any(mt.lower() in type_l for mt in ['Like Received', 'Profile View', 'Interactive Poll', 'Challenge Issued']): bs = 0.5
        elif any(lt.lower() in type_l for lt in ['Special Offer', 'Premium Feature', 'Inactive Reminder', 'General Announcement']): bs = 0.2
        if len(content) > 150: bs *= 0.9
        if '?' in content: bs = min(1.0, bs * 1.15)
        bs = min(1.0, bs * (1 + len(re.findall(r'[😀-🙏]', content)) * 0.05))
        return min(max(bs, 0.1), 1.0)
    def determine_context_requirements(self, nt: str, pl: List[str]) -> Dict[str, Any]:
        req = {}
        if 'match_name' in pl: req['has_recent_matches'] = True
        if 'sender_name' in pl: req['has_recent_messages'] = True
        if 'character_name' in pl: req['has_chat_characters'] = True
        if 'user_body_part' in pl or 'body_part' in pl: req['nsfw_tolerance_min'] = 2
        if 'Premium' in nt or 'Offer' in nt: req['subscription_status'] = 'free'
        if 'Inactive' in nt: req['low_recent_activity'] = True
        if 'Match' in nt: req['has_recent_matches'] = True
        return req

    def setup_category_mappings(self):
        self.logger.debug("Đang thiết lập ánh xạ danh mục.")
        self.category_mapping.clear()
        for tid, t in self.templates.items(): self.category_mapping[t.category].append(tid)
        self.all_loaded_categories = set(self.category_mapping.keys())
        self.logger.debug(f"Hoàn thành thiết lập ánh xạ, số danh mục duy nhất: {len(self.all_loaded_categories)}")

    def get_all_categories(self) -> List[str]: return sorted(list(self.all_loaded_categories))

    def get_suitable_notifications(self, uc: UserContext) -> List[NotificationTemplate]:
        self.logger.debug(f"Đang lấy các thông báo phù hợp cho người dùng: {uc.user_id}")
        s = []; prefs = uc.notification_preferences
        for t in self.templates.values():
            if t.nsfw_level > prefs.nsfw_tolerance: continue
            if t.category in prefs.blocked_categories: continue
            if prefs.preferred_categories and t.category not in prefs.preferred_categories: continue
            if self.meets_context_requirements(t, uc): s.append(t)
        self.logger.debug(f"Tìm thấy {len(s)} thông báo phù hợp cho người dùng {uc.user_id}")
        return s
        
    def meets_context_requirements(self, t: NotificationTemplate, uc: UserContext) -> bool:
        req = t.user_context_requirements
        if not req: return True
        if 'subscription_status' in req and uc.subscription_status != req['subscription_status']: return False
        if req.get('has_recent_matches', False) and not uc.recent_chat_characters: return False
        if req.get('has_recent_messages', False) and uc.messages_per_day < 1: return False
        if req.get('has_chat_characters', False) and uc.num_chat_characters == 0: return False
        if 'nsfw_tolerance_min' in req and uc.notification_preferences.nsfw_tolerance < req['nsfw_tolerance_min']: return False
        if req.get('low_recent_activity', False) and uc.recent_activity_level > 0.3: return False
        return True
        
    def personalize_notification(self, t: NotificationTemplate, uc: UserContext) -> Tuple[str, str]:
        title = t.title; content = t.content
        return title, content

class EnhancedNotificationBandit:
    def __init__(self, content_manager: NotificationContentManager, alpha: float = 0.1):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(f"Khởi tạo Bandit cho content_manager có {len(content_manager.templates)} templates.")
        self.content_manager = content_manager
        self.alpha = alpha
        self.feature_extractor = EnhancedFeatureExtractor()

        self.category_performance = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.debug(f"Thuộc tính self.category_performance được tạo: {type(self.category_performance)}")
        if hasattr(self, 'category_performance'): self.logger.debug("Xác nhận category_performance tồn tại ngay sau khi tạo.")
        else: self.logger.critical("LỖI NGAY LẬP TỨC - category_performance KHÔNG tồn tại sau khi tạo trong __init__.")

        self.time_performance = defaultdict(lambda: defaultdict(lambda: {'sent': 0, 'clicked': 0}))
        self.logger.debug(f"Thuộc tính self.time_performance được tạo: {type(self.time_performance)}")
        if hasattr(self, 'time_performance'): self.logger.debug("Xác nhận time_performance tồn tại ngay sau khi tạo.")
        else: self.logger.critical("LỖI NGAY LẬP TỨC - time_performance KHÔNG tồn tại sau khi tạo trong __init__.")
        
        self.template_ids: List[str] = []
        self.n_arms: int = 0
        self.n_features: int = self.feature_extractor.get_feature_count()
        self.A: List[np.ndarray] = []
        self.b: List[np.ndarray] = []
        self.arm_performance: Dict[str, Dict[str, Any]] = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.info(f"Bandit khởi tạo hoàn tất (chưa khởi tạo nhánh cụ thể).")

    def _initialize_arms(self):
        self.logger.info(f"_initialize_arms: Bắt đầu. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
        self.template_ids = list(self.content_manager.templates.keys())
        self.n_arms = len(self.template_ids)
        if self.n_features != self.feature_extractor.get_feature_count():
             self.logger.warning(f"Số lượng feature không nhất quán! FeatureExtractor: {self.feature_extractor.get_feature_count()}, Bandit: {self.n_features}. Đang cập nhật bandit.")
             self.n_features = self.feature_extractor.get_feature_count()

        self.A = [np.eye(self.n_features) for _ in range(self.n_arms)]
        self.b = [np.zeros(self.n_features) for _ in range(self.n_arms)]
        self.arm_performance = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.info(f"_initialize_arms: Hoàn thành. Đã tạo {self.n_arms} nhánh. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")

    def select_notification(self, uc: UserContext, cand_temps: Optional[List[NotificationTemplate]] = None, diversity_factor: float = 0.3) -> Optional[Tuple[NotificationTemplate, float]]:
        self.logger.debug(f"select_notification cho người dùng {uc.user_id}. Số template_ids hiện tại: {len(self.template_ids)}")
        if not self.template_ids:
             self.logger.warning("select_notification: Không có mẫu nào để chọn (template_ids rỗng).")
             return None, 0.0
        
        c_temps = cand_temps if cand_temps is not None else self.content_manager.get_suitable_notifications(uc)
        if not c_temps:
            self.logger.debug(f"Không có mẫu phù hợp ban đầu cho {uc.user_id}, đang thử fallback SFW.")
            c_temps = [t for t in self.content_manager.templates.values() if t.nsfw_level <= uc.notification_preferences.nsfw_tolerance]
            if not c_temps:
                self.logger.warning(f"Không có mẫu SFW fallback nào cho {uc.user_id}.")
                return None, 0.0
        
        # Tính UCB scores cho tất cả candidates
        candidate_scores = []
        feats = self.feature_extractor.extract_features(uc)
        curr_t_map = {tid: i for i, tid in enumerate(self.template_ids)}

        for t in c_temps:
            arm_idx = -1
            if t.notification_id not in curr_t_map:
                try: arm_idx = self.template_ids.index(t.notification_id)
                except ValueError: self.logger.warning(f"Mẫu {t.notification_id} không có trong template_ids của bandit khi chọn."); continue
            else: arm_idx = curr_t_map[t.notification_id]
            
            if arm_idx >= len(self.A) or arm_idx < 0: 
                self.logger.error(f"Chỉ số nhánh không hợp lệ {arm_idx} cho mẫu {t.notification_id}"); continue
            
            try:
                A_inv = np.linalg.inv(self.A[arm_idx])
                theta = A_inv @ self.b[arm_idx]
                mr = feats @ theta; cr = self.alpha * np.sqrt(feats @ A_inv @ feats.T)
                ucb = mr + cr + t.engagement_score * 0.2
                
                # Bonus nhỏ hơn cho preferred categories để không quá thiên vị
                if t.category in uc.notification_preferences.preferred_categories: ucb += 0.15
                # Giảm bonus cho preferred hours để có distribution đều hơn
                if uc.hour_of_day in uc.notification_preferences.preferred_hours: ucb += 0.1
                
                # Thêm diversity bonus cho categories ít được chọn
                category_usage = self.category_performance.get(t.category, {'sent': 0})['sent']
                total_sent = sum(cat_data['sent'] for cat_data in self.category_performance.values())
                if total_sent > 0:
                    category_frequency = category_usage / total_sent
                    diversity_bonus = (1 - category_frequency) * diversity_factor
                    ucb += diversity_bonus
                
                candidate_scores.append((t, ucb))
                
            except np.linalg.LinAlgError:
                self.logger.error(f"Lỗi LinAlgError (có thể ma trận A suy biến) cho nhánh {arm_idx} (mẫu {t.notification_id}). Bỏ qua nhánh này.", exc_info=False)
                continue
            except Exception as e:
                self.logger.error(f"Lỗi không mong muốn khi tính UCB cho nhánh {arm_idx} (mẫu {t.notification_id}): {e}", exc_info=True)
                continue

        if not candidate_scores:
            if c_temps:
                self.logger.debug(f"Không tính được UCB, chọn ngẫu nhiên từ các ứng viên cho {uc.user_id}.")
                best_t = random.choice(c_temps); best_s = 0.0
                return best_t, best_s
            return None, 0.0
        
        # Sắp xếp theo score và thêm element of randomness
        candidate_scores.sort(key=lambda x: x[1], reverse=True)
        
        # Chọn từ top candidates với probability distribution
        top_n = min(3, len(candidate_scores))  # Chọn từ top 3
        weights = [0.5, 0.3, 0.2][:top_n]  # Probability weights
        
        selected_idx = random.choices(range(top_n), weights=weights[:top_n])[0]
        best_t, best_s = candidate_scores[selected_idx]
        
        self.logger.debug(f"Đã chọn mẫu '{best_t.notification_id}' (rank {selected_idx+1}) cho {uc.user_id} với điểm {best_s:.2f}")
        return best_t, best_s

    def update(self, template: NotificationTemplate, user_context: UserContext, clicked: bool):
        if not hasattr(self, 'category_performance'):
            self.logger.critical("CẬP NHẬT BANDIT: 'category_performance' BỊ THIẾU! Đang khởi tạo lại."); self.category_performance = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        if not hasattr(self, 'time_performance'):
            self.logger.critical("CẬP NHẬT BANDIT: 'time_performance' BỊ THIẾU! Đang khởi tạo lại."); self.time_performance = defaultdict(lambda: defaultdict(lambda: {'sent': 0, 'clicked': 0}))

        self.logger.debug(f"Cập nhật bandit cho mẫu {template.notification_id}, người dùng {user_context.user_id}, đã nhấp: {clicked}")
        reward = 1.0 if clicked else 0.0
        try:
            arm_idx = self.template_ids.index(template.notification_id)
            if arm_idx < len(self.A):
                features = self.feature_extractor.extract_features(user_context)
                self.A[arm_idx] += np.outer(features, features); self.b[arm_idx] += reward * features
            else:
                self.logger.warning(f"Cập nhật bandit: Chỉ số nhánh {arm_idx} không hợp lệ cho mẫu {template.notification_id} khi cập nhật trọng số.")
        except ValueError:
             self.logger.warning(f"Cập nhật bandit: Mẫu {template.notification_id} không tìm thấy trong template_ids. Không cập nhật trọng số bandit.")
        except Exception as e:
            self.logger.error(f"Lỗi không mong muốn khi cập nhật trọng số bandit cho mẫu {template.notification_id}: {e}", exc_info=True)

        self.arm_performance[template.notification_id]['sent'] += 1
        if clicked: self.arm_performance[template.notification_id]['clicked'] += 1
        if self.arm_performance[template.notification_id]['sent'] > 0:
             self.arm_performance[template.notification_id]['ctr'] = self.arm_performance[template.notification_id]['clicked'] / self.arm_performance[template.notification_id]['sent']

        cat_str = template.category
        self.category_performance[cat_str]['sent'] += 1
        if clicked: self.category_performance[cat_str]['clicked'] += 1
        if self.category_performance[cat_str]['sent'] > 0:
            self.category_performance[cat_str]['ctr'] = self.category_performance[cat_str]['clicked'] / self.category_performance[cat_str]['sent']

        hour = user_context.hour_of_day
        self.time_performance[hour][template.notification_id]['sent'] += 1
        if clicked: self.time_performance[hour][template.notification_id]['clicked'] += 1
        self.logger.debug(f"Hoàn thành cập nhật bandit cho mẫu {template.notification_id}.")

    def ensure_arms_updated(self, content_manager: NotificationContentManager):
        self.logger.info(f"ensure_arms_updated: Bắt đầu. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
        self.content_manager = content_manager
        new_template_ids = list(self.content_manager.templates.keys())
        needs_update = not self.template_ids or set(self.template_ids) != set(new_template_ids)

        if needs_update:
            self.logger.info("ensure_arms_updated: Phát hiện thay đổi mẫu hoặc khởi tạo lần đầu. Đang đồng bộ hóa các nhánh bandit.")
            self.logger.debug(f"ensure_arms_updated: TRƯỚC _initialize_arms. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
            self._initialize_arms()
            self.logger.debug(f"ensure_arms_updated: SAU _initialize_arms. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")
            self.logger.info(f"ensure_arms_updated: Đã khởi tạo lại/đồng bộ {self.n_arms} nhánh.")
        else:
            self.logger.info(f"ensure_arms_updated: Không có thay đổi mẫu. category_performance tồn tại: {hasattr(self, 'category_performance')}, time_performance tồn tại: {hasattr(self, 'time_performance')}")

class EnhancedFeatureExtractor:
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.sub_enc = {"free": 0, "premium": 1, "premium_plus": 2}
        self.freq_enc = {"low": 0, "medium": 1, "high": 2}
        self.mood_enc = {"sad": 0, "neutral": 1, "happy": 2, "excited": 3, "lonely": 4, "bored": 0, "stressed":0}
        self.rel_enc = {"single": 0, "dating": 1, "relationship": 2, "complicated": 3, "exploring": 1}
        self._feature_count = 25 
        self.logger.debug("EnhancedFeatureExtractor đã khởi tạo.")

    def get_feature_count(self) -> int: return self._feature_count
    def extract_features(self, uc: UserContext) -> np.ndarray:
        fts = []; pr = uc.notification_preferences
        fts.append(self.sub_enc.get(uc.subscription_status, 0) / 2.0)
        fts.append(min(uc.gifts_given / 10.0, 1.0)); fts.append(min(uc.diamonds_spent / 1000.0, 1.0))
        fts.append(min(uc.num_chat_characters / 10.0, 1.0)); fts.append(min(uc.messages_per_day / 50.0, 1.0))
        fts.append(uc.notification_click_rate); fts.append(min(max(uc.age - 18, 0) / (65-18), 1.0))
        fts.append(1.0 if uc.gender.lower() == 'female' else 0.0)
        fts.append(self.rel_enc.get(uc.interests_target.lower(), 0) / 3.0)
        fts.append(uc.day_of_week / 6.0); fts.append(uc.hour_of_day / 23.0)
        fts.append(self.freq_enc.get(pr.notification_frequency.lower(), 1) / 2.0)
        fts.append(len(pr.preferred_hours) / 24.0); fts.append(len(pr.preferred_days) / 7.0)
        fts.append(pr.nsfw_tolerance / 3.0)
        max_cat_c = len(KNOWN_NOTIFICATION_CATEGORIES) if KNOWN_NOTIFICATION_CATEGORIES else 12
        fts.append(len(pr.preferred_categories) / max(1, max_cat_c))
        fts.append(uc.recent_activity_level); fts.append(min(len(uc.recent_chat_characters) / 5.0, 1.0))
        fts.append(1.0 if uc.recent_chat_characters else 0.0)
        fts.append(uc.cumulative_clicks / max(1.0, uc.cumulative_notifications))
        fts.append(self.mood_enc.get(uc.current_mood.lower(), 1) / 4.0)
        hrs_online = max(0, (uc.date - uc.last_online_time).total_seconds() / 3600)
        fts.append(min(hrs_online / 72.0, 1.0))
        fts.extend([float(uc.hour_of_day in pr.preferred_hours), float(uc.day_of_week in pr.preferred_days)])
        fts.append(float(uc.day_of_week >= 5))
        curr_len = len(fts)
        if curr_len > self._feature_count: fts = fts[:self._feature_count]
        elif curr_len < self._feature_count: fts.extend([0.0] * (self._feature_count - curr_len))
        return np.array(fts)

class NotificationRecommendationSystem:
    def __init__(self, notification_json_data: Optional[Dict[str, Any]]):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info("Khởi tạo NotificationRecommendationSystem.")
        self.content_manager = NotificationContentManager(notification_json_data)
        self.logger.info(f"ContentManager được tạo với {len(self.content_manager.templates)} templates.")
        self.bandit = EnhancedNotificationBandit(self.content_manager)
        self.logger.info(f"Bandit được tạo. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}, time_performance tồn tại: {hasattr(self.bandit, 'time_performance')}")
        self.bandit.ensure_arms_updated(self.content_manager)
        self.logger.info(f"Sau ensure_arms_updated. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}, time_performance tồn tại: {hasattr(self.bandit, 'time_performance')}")
        self.total_sent = 0; self.total_clicked = 0
        self.user_metrics = defaultdict(lambda: {'sent': 0, 'clicked': 0, 'ctr': 0.0})
        self.logger.info("NotificationRecommendationSystem khởi tạo hoàn tất.")

    def reload_notifications(self, notification_json_data: Optional[Dict[str, Any]]):
        self.logger.info(f"Bắt đầu tải lại thông báo.")
        try:
            self.content_manager = NotificationContentManager(notification_json_data)
            self.logger.info(f"ContentManager mới được tạo với {len(self.content_manager.templates)} templates.")
            if self.bandit: self.logger.debug(f"TRƯỚC ensure_arms_updated khi reload. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}")
            else: self.logger.warning("TRƯỚC ensure_arms_updated khi reload. Bandit chưa được tạo.")
            self.bandit.ensure_arms_updated(self.content_manager)
            self.logger.debug(f"SAU ensure_arms_updated khi reload. category_performance tồn tại: {hasattr(self.bandit, 'category_performance')}")
            self.logger.info(f"Đã tải lại {len(self.content_manager.templates)} mẫu.")
        except Exception as e:
            self.logger.critical(f"LỖI NGHIÊM TRỌNG khi tải lại thông báo: {e}", exc_info=True)

    def get_recommendation(self, uc: UserContext) -> Optional[Dict[str, Any]]:
        self.logger.debug(f"get_recommendation cho người dùng {uc.user_id}")
        t, conf = self.bandit.select_notification(uc)
        if not t:
            self.logger.info(f"Không có đề xuất nào cho người dùng {uc.user_id}")
            return None
        title, content = self.content_manager.personalize_notification(t, uc)
        self.logger.info(f"Đề xuất cho người dùng {uc.user_id}: Mẫu {t.notification_id}, Tiêu đề '{title[:30]}...'")
        return {'template_id': t.notification_id, 'title': title, 'content': content, 'category': t.category,
                'notification_type': t.notification_type, 'nsfw_level': t.nsfw_level,
                'confidence_score': conf if conf is not None else 0.0, 'expected_engagement': t.engagement_score,
                'recommended_time': uc.date, 'user_id': uc.user_id}

    def record_interaction(self, rec: Dict[str, Any], clicked: bool, uc_update: UserContext):
        tid = rec['template_id']
        self.logger.debug(f"Ghi lại tương tác cho người dùng {rec['user_id']}, mẫu {tid}, đã nhấp: {clicked}")
        if tid not in self.content_manager.templates:
            self.logger.error(f"Mẫu ID {tid} từ đề xuất không tìm thấy trong ContentManager khi ghi lại tương tác.")
            return
        t = self.content_manager.templates[tid]
        self.bandit.update(t, uc_update, clicked)
        self.total_sent += 1
        if clicked: self.total_clicked += 1
        uid = rec['user_id']
        self.user_metrics[uid]['sent'] += 1
        if clicked: self.user_metrics[uid]['clicked'] += 1
        if self.user_metrics[uid]['sent'] > 0: self.user_metrics[uid]['ctr'] = self.user_metrics[uid]['clicked'] / self.user_metrics[uid]['sent']
        self.logger.debug(f"Hoàn thành ghi lại tương tác cho mẫu {tid}.")

    def get_performance_report(self) -> Dict[str, Any]:
        self.logger.info("Đang tạo báo cáo hiệu suất.")
        ov_ctr = (self.total_clicked / self.total_sent) if self.total_sent > 0 else 0.0
        cat_stats = {}
        bandit_cat_perf = self.bandit.category_performance if hasattr(self.bandit, 'category_performance') else {}
        for cat_s, stats in bandit_cat_perf.items():
            cat_stats[cat_s] = {'sent': stats['sent'], 'clicked': stats['clicked'], 'ctr': stats['ctr'] if stats['sent'] > 0 else 0.0}
        
        bandit_arm_perf = self.bandit.arm_performance if hasattr(self.bandit, 'arm_performance') else {}
        valid_arm_perf = {arm_id: stats for arm_id, stats in bandit_arm_perf.items() if arm_id in self.content_manager.templates}
        top_ts = sorted(valid_arm_perf.items(), key=lambda x: x[1]['ctr'] if x[1]['sent'] > 0 else -1, reverse=True)[:10]
        
        report_data = {'overall_ctr': ov_ctr, 'total_sent': self.total_sent, 'total_clicked': self.total_clicked,
                       'category_performance': cat_stats,
                       'top_templates': [{'template_id': tid, 'type': self.content_manager.templates[tid].notification_type,
                                          'category': self.content_manager.templates[tid].category,
                                          'title': self.content_manager.templates[tid].title[:60] + "...",
                                          'ctr': s['ctr'], 'sent': s['sent'], 'clicked': s['clicked']} for tid, s in top_ts]}
        self.logger.info("Tạo báo cáo hiệu suất hoàn thành.")
        return report_data

logger_main = logging.getLogger("MAIN_SIMULATION")

def create_user_context(user_profile: Dict[str, Any]) -> UserContext:
    safe_cs = list(KNOWN_NOTIFICATION_CATEGORIES)
    num_pref = random.randint(0, min(5, len(safe_cs)))
    pref_cs = random.sample(safe_cs, num_pref) if num_pref > 0 and safe_cs else []
    return UserContext(
        user_id=user_profile.get('id', 'unknown'), 
        date=datetime.now(), 
        subscription_status=user_profile.get('subscription_status', 'free'),
        interests_target=user_profile.get('interests_target', 'nsfw'),
        gender=user_profile.get('gender', 'unknown'),
        age=user_profile.get('age', 25), 
        gifts_given=user_profile.get('gifts_given', 0), 
        diamonds_spent=user_profile.get('diamonds_spent', 0.0),
        num_chat_characters=user_profile.get('num_chat_characters', 0), 
        messages_per_day=user_profile.get('messages_per_day', 0),
        notification_click_rate=user_profile.get('notification_click_rate', 0.25),
        recent_chat_characters=user_profile.get('recent_chat_characters', []),
        top_chat_characters=user_profile.get('top_chat_characters', []),
        online_time_periods=user_profile.get('online_time_periods', []),
        last_online_time=user_profile.get('last_online_time', datetime.now()),
        day_of_week=user_profile.get('day_of_week', datetime.now().weekday()),
        cumulative_notifications=user_profile.get("cumulative_notifications", 100),
        preferred_character_types=user_profile.get('preferred_character_types', ['friendly', 'romantic']), 
        current_mood=user_profile.get('current_mood', 'happy'),
        hour_of_day=user_profile.get('hour_of_day', datetime.now().hour),
        cumulative_clicks=user_profile.get('cumulative_clicks', 25),
        recent_activity_level=user_profile.get('recent_activity_level', 0.7),
        notification_preferences=UserNotificationPreferences(
            user_id=user_profile.get('id', 'unknown'), preferred_hours=sorted(random.sample(range(24), random.randint(3, 6))),
            preferred_days=sorted(random.sample(range(7), random.randint(2, 5))),
            max_notifications_per_day=random.randint(1, 4), time_zone_offset=random.choice([-7, 0, 5]),
            do_not_disturb_start=random.choice([22, 23, 0]), do_not_disturb_end=random.choice([6, 7, 8]),
            notification_frequency=random.choice(['low', 'medium', 'high']), nsfw_tolerance=random.randint(0, 3),
            preferred_categories=pref_cs, blocked_categories=[]))

def bandit_algo_running(system: NotificationRecommendationSystem, users_ctxs: List[UserContext]):
    logger_main.info(f"🚀 Bắt đầu mô phỏng ngày...")
    loaded_cats = system.content_manager.get_all_categories()
    notifications = {}
    if not loaded_cats:
        loaded_cats = list(KNOWN_NOTIFICATION_CATEGORIES)
        logger_main.warning("Mô phỏng: Không có danh mục nào được tải, sử dụng danh mục mặc định đã biết.")
    random.shuffle(users_ctxs)
    for idx, uc in enumerate(users_ctxs):
        notification = {
            "user_id": uc.user_id,
            "character_list": uc.top_chat_characters,
            "rec": []
        }
        for _ in range(random.randint(3, 6)):
            uc.date = datetime.now() + timedelta(days=0, hours=random.uniform(0,24 - datetime.now().hour))
            uc.day_of_week = uc.date.weekday(); uc.hour_of_day = uc.date.hour
            uc.current_mood = random.choice(['happy', 'lonely', 'neutral'])
            uc.recent_activity_level = max(0, min(1, uc.recent_activity_level + random.uniform(-0.1, 0.1)))
            rec = system.get_recommendation(uc)
            if rec:
                prob = 0.1 + rec['expected_engagement'] * 0.2
                if rec['category'] in uc.notification_preferences.preferred_categories: prob += 0.15
                if rec['category'] in uc.notification_preferences.blocked_categories: prob -= 0.2
                if rec['nsfw_level'] <= uc.notification_preferences.nsfw_tolerance: prob += 0.1
                else: prob -= 0.15
                if uc.hour_of_day in uc.notification_preferences.preferred_hours: prob += 0.05
                clicked = random.random() < max(0.01, min(prob, 0.9))
                system.record_interaction(rec, clicked, uc)
                notification["rec"].append(rec)
            else:
                logger_main.debug("Không có đề xuất nào cho người dùng")
        notifications[uc.user_id] = notification
    return notifications

def run_bandit(users_ctxs: List[UserContext], notification_json_data: Optional[Dict[str, Any]]):
    system = NotificationRecommendationSystem(notification_json_data)
    return bandit_algo_running(system, users_ctxs)