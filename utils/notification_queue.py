"""
Notification Queue Manager using BullMQ
Handles notification task creation, processing, and queue management.
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
import redis
from bullmq import Queue, Worker, Job
from bullmq.types import QueueBaseOptions, WorkerOptions

# Import environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Setup logging
logger = logging.getLogger(__name__)


@dataclass
class NotificationTask:
    """Notification task data structure"""
    notification_id: str
    user_id: str
    title: str
    content: str
    category: str
    template_id: Optional[str] = None
    priority: int = 0  # Higher number = higher priority
    delay: int = 0  # Delay in seconds
    scheduled_time: Optional[datetime] = None
    send_at_hour: Optional[int] = None  # Send at specific hour (0-23)
    send_at_minute: Optional[int] = None  # Send at specific minute (0-59)
    send_on_date: Optional[str] = None  # Send on specific date (YYYY-MM-DD)
    send_after_minutes: Optional[int] = None  # Send after X minutes from now
    send_after_hours: Optional[int] = None  # Send after X hours from now
    send_after_days: Optional[int] = None  # Send after X days from now
    recurring: bool = False  # Whether this is a recurring notification
    recurring_interval: Optional[str] = None  # daily, weekly, monthly
    retry_attempts: int = 3
    metadata: Optional[Dict[str, Any]] = None


class NotificationQueueManager:
    """
    Manages notification queues using BullMQ
    """

    def __init__(self,
                 redis_url: str = None,
                 queue_name: str = "notifications",
                 max_concurrency: int = 10):
        """
        Initialize notification queue manager

        Args:
            redis_url: Redis connection URL
            queue_name: Name of the notification queue
            max_concurrency: Maximum concurrent workers
        """
        self.redis_url = redis_url or os.getenv('REDIS_URL', 'redis://localhost:6379')
        self.queue_name = queue_name
        self.max_concurrency = max_concurrency

        # Parse Redis URL to get connection options
        self.connection_opts = self._parse_redis_url(self.redis_url)

        # Test Redis connection
        try:
            self.redis_client = redis.from_url(self.redis_url)
            self.redis_client.ping()
            logger.info(f"Connected to Redis: {self.redis_url}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {e}")
            raise

        # Initialize BullMQ queue with connection options
        queue_opts = QueueBaseOptions(connection=self.connection_opts)
        self.queue = Queue(self.queue_name, queue_opts)

        # Worker instance (will be set when starting worker)
        self.worker: Optional[Worker] = None
        self.is_worker_running = False

        # Notification processors
        self.processors: Dict[str, Callable] = {}

        logger.info(f"NotificationQueueManager initialized with queue: {self.queue_name}")

    def _parse_redis_url(self, redis_url: str) -> Dict[str, Any]:
        """
        Parse Redis URL to connection options

        Args:
            redis_url: Redis connection URL

        Returns:
            Dict: Connection options for BullMQ
        """
        try:
            from urllib.parse import urlparse

            parsed = urlparse(redis_url)

            connection_opts = {
                "host": parsed.hostname,
                "port": parsed.port or 6379,
            }

            if parsed.username:
                connection_opts["username"] = parsed.username

            if parsed.password:
                connection_opts["password"] = parsed.password

            if parsed.path and len(parsed.path) > 1:
                # Remove leading '/' and convert to int
                connection_opts["db"] = int(parsed.path[1:])

            return connection_opts

        except Exception as e:
            logger.error(f"Error parsing Redis URL: {e}")
            # Fallback to localhost
            return {
                "host": "localhost",
                "port": 6379
            }

    def _calculate_send_time(self, task: NotificationTask) -> Optional[datetime]:
        """
        Calculate the actual send time based on task parameters

        Args:
            task: NotificationTask object

        Returns:
            datetime: Calculated send time or None for immediate send
        """
        now = datetime.utcnow()
        send_time = None

        # Priority order: scheduled_time > send_on_date + time > send_after_* > delay

        # 1. If scheduled_time is explicitly set, use it
        if task.scheduled_time:
            return task.scheduled_time

        # 2. If send_on_date is set, combine with time
        if task.send_on_date:
            try:
                # Parse date
                date_parts = task.send_on_date.split('-')
                year, month, day = int(date_parts[0]), int(date_parts[1]), int(date_parts[2])

                # Set time
                hour = task.send_at_hour if task.send_at_hour is not None else 9  # Default 9 AM
                minute = task.send_at_minute if task.send_at_minute is not None else 0

                send_time = datetime(year, month, day, hour, minute)

                # If the time has passed today and it's the same date, schedule for tomorrow
                if send_time <= now and send_time.date() == now.date():
                    send_time = send_time + timedelta(days=1)

                return send_time

            except (ValueError, IndexError) as e:
                logger.error(f"Invalid send_on_date format: {task.send_on_date}. Use YYYY-MM-DD")
                return None

        # 3. If send_at_hour/minute is set for today
        if task.send_at_hour is not None:
            hour = task.send_at_hour
            minute = task.send_at_minute if task.send_at_minute is not None else 0

            send_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)

            # If the time has passed today, schedule for tomorrow
            if send_time <= now:
                send_time = send_time + timedelta(days=1)

            return send_time

        # 4. If send_after_* is set
        if task.send_after_days:
            return now + timedelta(days=task.send_after_days)

        if task.send_after_hours:
            return now + timedelta(hours=task.send_after_hours)

        if task.send_after_minutes:
            return now + timedelta(minutes=task.send_after_minutes)

        # 5. If delay is set
        if task.delay > 0:
            return now + timedelta(seconds=task.delay)

        # 6. No scheduling, send immediately
        return None

    async def add_notification_task(self, task: NotificationTask) -> Optional[str]:
        """
        Add notification task to queue

        Args:
            task: NotificationTask object

        Returns:
            str: Job ID if successful, None otherwise
        """
        try:
            # Calculate actual send time
            calculated_send_time = self._calculate_send_time(task)

            # Convert task to dict
            task_data = asdict(task)

            # Handle datetime serialization
            if task_data.get('scheduled_time'):
                task_data['scheduled_time'] = task_data['scheduled_time'].isoformat()

            # Add calculated send time to metadata
            if calculated_send_time:
                task_data['calculated_send_time'] = calculated_send_time.isoformat()

            # Set job options
            job_options = {
                'priority': task.priority,
                'attempts': task.retry_attempts,
                'backoff': {
                    'type': 'exponential',
                    'delay': 2000  # 2 seconds base delay
                }
            }

            # Calculate delay in milliseconds
            if calculated_send_time:
                delay_ms = int((calculated_send_time - datetime.utcnow()).total_seconds() * 1000)
                if delay_ms > 0:
                    job_options['delay'] = delay_ms
                    logger.info(f"Scheduling notification for {calculated_send_time} (delay: {delay_ms/1000:.1f}s)")

            # Handle recurring notifications
            if task.recurring and task.recurring_interval:
                # Add recurring metadata
                task_data['is_recurring'] = True
                task_data['recurring_interval'] = task.recurring_interval
                task_data['original_send_time'] = calculated_send_time.isoformat() if calculated_send_time else None

            # Add job to queue
            job = await self.queue.add(
                name=f"notification_{task.category}",
                data=task_data,
                opts=job_options
            )

            logger.info(f"Added notification task to queue: {job.id} for user {task.user_id}")
            return job.id

        except Exception as e:
            logger.error(f"Error adding notification task to queue: {e}")
            return None

    async def add_bulk_notification_tasks(self, tasks: List[NotificationTask]) -> List[str]:
        """
        Add multiple notification tasks to queue

        Args:
            tasks: List of NotificationTask objects

        Returns:
            List[str]: List of job IDs
        """
        job_ids = []

        try:
            # Prepare bulk data
            bulk_data = []
            for task in tasks:
                task_data = asdict(task)

                # Handle datetime serialization
                if task_data.get('scheduled_time'):
                    task_data['scheduled_time'] = task_data['scheduled_time'].isoformat()

                job_options = {
                    'priority': task.priority,
                    'attempts': task.retry_attempts,
                    'backoff': {
                        'type': 'exponential',
                        'delay': 2000
                    }
                }

                if task.delay > 0:
                    job_options['delay'] = task.delay * 1000

                if task.scheduled_time:
                    delay_ms = int((task.scheduled_time - datetime.utcnow()).total_seconds() * 1000)
                    if delay_ms > 0:
                        job_options['delay'] = delay_ms

                bulk_data.append({
                    'name': f"notification_{task.category}",
                    'data': task_data,
                    'opts': job_options
                })

            # Add bulk jobs
            jobs = await self.queue.add_bulk(bulk_data)
            job_ids = [job.id for job in jobs]

            logger.info(f"Added {len(job_ids)} notification tasks to queue")

        except Exception as e:
            logger.error(f"Error adding bulk notification tasks: {e}")

        return job_ids

    def register_processor(self, category: str, processor_func: Callable):
        """
        Register a processor function for a specific notification category

        Args:
            category: Notification category
            processor_func: Async function to process notifications
        """
        self.processors[category] = processor_func
        logger.info(f"Registered processor for category: {category}")

    async def process_notification_job(self, job: Job) -> Any:
        """
        Process a notification job

        Args:
            job: BullMQ job object

        Returns:
            Any: Processing result
        """
        try:
            task_data = job.data
            category = task_data.get('category', 'default')

            logger.info(f"Processing notification job {job.id} for user {task_data.get('user_id')}")

            # Get processor for category
            processor = self.processors.get(category, self.processors.get('default'))

            if not processor:
                logger.error(f"No processor found for category: {category}")
                raise Exception(f"No processor for category: {category}")

            # Process the notification
            result = await processor(task_data)

            logger.info(f"Successfully processed notification job {job.id}")
            return result

        except Exception as e:
            logger.error(f"Error processing notification job {job.id}: {e}")
            raise

    async def start_worker(self):
        """Start the notification worker"""
        if self.is_worker_running:
            logger.warning("Worker is already running")
            return

        try:
            worker_opts = WorkerOptions(
                connection=self.connection_opts,
                concurrency=self.max_concurrency
            )

            self.worker = Worker(
                self.queue_name,
                self.process_notification_job,
                worker_opts
            )

            self.is_worker_running = True
            logger.info(f"Started notification worker with concurrency: {self.max_concurrency}")

            # Start processing
            await self.worker.run()

        except Exception as e:
            logger.error(f"Error starting worker: {e}")
            self.is_worker_running = False
            raise

    async def stop_worker(self):
        """Stop the notification worker"""
        if self.worker and self.is_worker_running:
            try:
                await self.worker.close()
                self.is_worker_running = False
                logger.info("Stopped notification worker")
            except Exception as e:
                logger.error(f"Error stopping worker: {e}")

    async def get_queue_stats(self) -> Dict[str, Any]:
        """
        Get queue statistics

        Returns:
            Dict: Queue statistics
        """
        try:
            waiting = await self.queue.get_waiting_count()
            active = await self.queue.get_active_count()
            completed = await self.queue.get_completed_count()
            failed = await self.queue.get_failed_count()
            delayed = await self.queue.get_delayed_count()

            return {
                'waiting': waiting,
                'active': active,
                'completed': completed,
                'failed': failed,
                'delayed': delayed,
                'total': waiting + active + completed + failed + delayed
            }

        except Exception as e:
            logger.error(f"Error getting queue stats: {e}")
            return {}

    async def get_jobs(self, status: str = 'waiting', limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get jobs by status

        Args:
            status: Job status (waiting, active, completed, failed, delayed)
            limit: Maximum number of jobs to return

        Returns:
            List[Dict]: List of job data
        """
        try:
            jobs = await self.queue.get_jobs([status], 0, limit - 1)

            job_list = []
            for job in jobs:
                job_data = {
                    'id': job.id,
                    'name': job.name,
                    'data': job.data,
                    'timestamp': job.timestamp,
                    'attempts_made': job.attempts_made,
                    'progress': job.progress
                }

                if hasattr(job, 'finished_on'):
                    job_data['finished_on'] = job.finished_on
                if hasattr(job, 'failed_reason'):
                    job_data['failed_reason'] = job.failed_reason

                job_list.append(job_data)

            return job_list

        except Exception as e:
            logger.error(f"Error getting jobs: {e}")
            return []

    async def retry_failed_jobs(self, limit: int = 10) -> int:
        """
        Retry failed jobs

        Args:
            limit: Maximum number of jobs to retry

        Returns:
            int: Number of jobs retried
        """
        try:
            failed_jobs = await self.queue.get_jobs(['failed'], 0, limit - 1)
            retried_count = 0

            for job in failed_jobs:
                try:
                    await job.retry()
                    retried_count += 1
                    logger.info(f"Retried failed job: {job.id}")
                except Exception as e:
                    logger.error(f"Error retrying job {job.id}: {e}")

            logger.info(f"Retried {retried_count} failed jobs")
            return retried_count

        except Exception as e:
            logger.error(f"Error retrying failed jobs: {e}")
            return 0

    async def clean_old_jobs(self, grace_period_hours: int = 24) -> Dict[str, int]:
        """
        Clean old completed and failed jobs

        Args:
            grace_period_hours: Hours to keep jobs before cleaning

        Returns:
            Dict: Number of jobs cleaned by status
        """
        try:
            grace_period_ms = grace_period_hours * 60 * 60 * 1000

            # Clean completed jobs
            completed_cleaned = await self.queue.clean(grace_period_ms, 'completed')

            # Clean failed jobs
            failed_cleaned = await self.queue.clean(grace_period_ms, 'failed')

            result = {
                'completed': completed_cleaned,
                'failed': failed_cleaned,
                'total': completed_cleaned + failed_cleaned
            }

            logger.info(f"Cleaned {result['total']} old jobs")
            return result

        except Exception as e:
            logger.error(f"Error cleaning old jobs: {e}")
            return {'completed': 0, 'failed': 0, 'total': 0}

    async def pause_queue(self):
        """Pause the queue"""
        try:
            await self.queue.pause()
            logger.info("Queue paused")
        except Exception as e:
            logger.error(f"Error pausing queue: {e}")

    async def resume_queue(self):
        """Resume the queue"""
        try:
            await self.queue.resume()
            logger.info("Queue resumed")
        except Exception as e:
            logger.error(f"Error resuming queue: {e}")

    async def close(self):
        """Close connections and cleanup"""
        try:
            if self.worker and self.is_worker_running:
                await self.stop_worker()

            if self.queue:
                await self.queue.close()

            if self.redis_client:
                await self.redis_client.close()

            logger.info("NotificationQueueManager closed")

        except Exception as e:
            logger.error(f"Error closing NotificationQueueManager: {e}")


# ==================== CONVENIENCE FUNCTIONS ====================

def create_notification_task(notification_id: str,
                           user_id: str,
                           title: str,
                           content: str,
                           category: str,
                           **kwargs) -> NotificationTask:
    """
    Create a notification task

    Args:
        notification_id: Unique notification identifier
        user_id: Target user ID
        title: Notification title
        content: Notification content
        category: Notification category
        **kwargs: Additional task parameters

    Returns:
        NotificationTask: Created task object
    """
    return NotificationTask(
        notification_id=notification_id,
        user_id=user_id,
        title=title,
        content=content,
        category=category,
        **kwargs
    )


def create_scheduled_task(notification_id: str,
                         user_id: str,
                         title: str,
                         content: str,
                         category: str,
                         send_on_date: str,
                         send_at_hour: int = 9,
                         send_at_minute: int = 0,
                         **kwargs) -> NotificationTask:
    """
    Create a scheduled notification task for specific date and time

    Args:
        notification_id: Unique notification identifier
        user_id: Target user ID
        title: Notification title
        content: Notification content
        category: Notification category
        send_on_date: Date to send (YYYY-MM-DD format)
        send_at_hour: Hour to send (0-23, default: 9)
        send_at_minute: Minute to send (0-59, default: 0)
        **kwargs: Additional task parameters

    Returns:
        NotificationTask: Created scheduled task object
    """
    return NotificationTask(
        notification_id=notification_id,
        user_id=user_id,
        title=title,
        content=content,
        category=category,
        send_on_date=send_on_date,
        send_at_hour=send_at_hour,
        send_at_minute=send_at_minute,
        **kwargs
    )


def create_delayed_task(notification_id: str,
                       user_id: str,
                       title: str,
                       content: str,
                       category: str,
                       send_after_minutes: int = None,
                       send_after_hours: int = None,
                       send_after_days: int = None,
                       **kwargs) -> NotificationTask:
    """
    Create a delayed notification task

    Args:
        notification_id: Unique notification identifier
        user_id: Target user ID
        title: Notification title
        content: Notification content
        category: Notification category
        send_after_minutes: Send after X minutes
        send_after_hours: Send after X hours
        send_after_days: Send after X days
        **kwargs: Additional task parameters

    Returns:
        NotificationTask: Created delayed task object
    """
    return NotificationTask(
        notification_id=notification_id,
        user_id=user_id,
        title=title,
        content=content,
        category=category,
        send_after_minutes=send_after_minutes,
        send_after_hours=send_after_hours,
        send_after_days=send_after_days,
        **kwargs
    )


def create_recurring_task(notification_id: str,
                         user_id: str,
                         title: str,
                         content: str,
                         category: str,
                         recurring_interval: str,
                         send_at_hour: int = 9,
                         send_at_minute: int = 0,
                         **kwargs) -> NotificationTask:
    """
    Create a recurring notification task

    Args:
        notification_id: Unique notification identifier
        user_id: Target user ID
        title: Notification title
        content: Notification content
        category: Notification category
        recurring_interval: Interval (daily, weekly, monthly)
        send_at_hour: Hour to send (0-23, default: 9)
        send_at_minute: Minute to send (0-59, default: 0)
        **kwargs: Additional task parameters

    Returns:
        NotificationTask: Created recurring task object
    """
    return NotificationTask(
        notification_id=notification_id,
        user_id=user_id,
        title=title,
        content=content,
        category=category,
        recurring=True,
        recurring_interval=recurring_interval,
        send_at_hour=send_at_hour,
        send_at_minute=send_at_minute,
        **kwargs
    )


async def create_queue_manager(redis_url: str = None,
                              queue_name: str = "notifications") -> NotificationQueueManager:
    """
    Create and initialize a notification queue manager

    Args:
        redis_url: Redis connection URL
        queue_name: Queue name

    Returns:
        NotificationQueueManager: Initialized queue manager
    """
    return NotificationQueueManager(redis_url=redis_url, queue_name=queue_name)


# ==================== INTEGRATION HELPERS ====================

class NotificationQueueIntegration:
    """
    Integration helper for notification queue with existing systems
    """

    def __init__(self, queue_manager: NotificationQueueManager, db_manager=None):
        """
        Initialize integration helper

        Args:
            queue_manager: NotificationQueueManager instance
            db_manager: MongoDB manager instance (optional)
        """
        self.queue_manager = queue_manager
        self.db_manager = db_manager
        self.logger = logging.getLogger(self.__class__.__name__)

    async def queue_recommendation(self, recommendation: Dict[str, Any],
                                 user_context, delay: int = 0) -> Optional[str]:
        """
        Queue a notification recommendation

        Args:
            recommendation: Recommendation dict from notification system
            user_context: UserContext object
            delay: Delay in seconds before sending

        Returns:
            str: Job ID if successful
        """
        try:
            # Create notification task from recommendation
            task = NotificationTask(
                notification_id=recommendation.get('template_id', f"rec_{datetime.utcnow().timestamp()}"),
                user_id=user_context.user_id,
                title=recommendation['title'],
                content=recommendation['content'],
                category=recommendation['category'],
                template_id=recommendation.get('template_id'),
                priority=int(recommendation.get('confidence_score', 0) * 10),
                delay=delay,
                metadata={
                    'expected_engagement': recommendation.get('expected_engagement'),
                    'confidence_score': recommendation.get('confidence_score'),
                    'nsfw_level': recommendation.get('nsfw_level'),
                    'user_preferences': {
                        'nsfw_tolerance': user_context.notification_preferences.nsfw_tolerance,
                        'preferred_categories': user_context.notification_preferences.preferred_categories
                    }
                }
            )

            # Add to queue
            job_id = await self.queue_manager.add_notification_task(task)

            if job_id and self.db_manager:
                # Log to database if available
                notification_data = {
                    'user_id': user_context.user_id,
                    'template_id': recommendation.get('template_id'),
                    'title': recommendation['title'],
                    'content': recommendation['content'],
                    'category': recommendation['category'],
                    'job_id': job_id,
                    'status': 'queued'
                }
                self.db_manager.record_notification_sent(notification_data)

            return job_id

        except Exception as e:
            self.logger.error(f"Error queueing recommendation: {e}")
            return None

    async def queue_bulk_recommendations(self, recommendations_with_users: List[tuple],
                                       delay: int = 0) -> List[str]:
        """
        Queue multiple recommendations

        Args:
            recommendations_with_users: List of (recommendation, user_context) tuples
            delay: Delay in seconds before sending

        Returns:
            List[str]: List of job IDs
        """
        tasks = []

        for recommendation, user_context in recommendations_with_users:
            task = NotificationTask(
                notification_id=recommendation.get('template_id', f"rec_{datetime.utcnow().timestamp()}"),
                user_id=user_context.user_id,
                title=recommendation['title'],
                content=recommendation['content'],
                category=recommendation['category'],
                template_id=recommendation.get('template_id'),
                priority=int(recommendation.get('confidence_score', 0) * 10),
                delay=delay,
                metadata={
                    'expected_engagement': recommendation.get('expected_engagement'),
                    'confidence_score': recommendation.get('confidence_score'),
                    'nsfw_level': recommendation.get('nsfw_level')
                }
            )
            tasks.append(task)

        return await self.queue_manager.add_bulk_notification_tasks(tasks)

    async def setup_default_processors(self):
        """Setup default notification processors"""

        async def send_push_notification(task_data: dict) -> dict:
            """Send push notification"""
            self.logger.info(f"📱 Sending push notification to user {task_data['user_id']}")

            # Here you would integrate with actual push notification service
            # Example: Firebase Cloud Messaging, Apple Push Notification Service

            # Simulate sending
            await asyncio.sleep(0.1)

            result = {
                'status': 'sent',
                'channel': 'push',
                'notification_id': task_data['notification_id'],
                'user_id': task_data['user_id'],
                'sent_at': datetime.utcnow().isoformat()
            }

            # Log to database if available
            if self.db_manager:
                self.db_manager.record_notification_click(
                    task_data.get('notification_id'),
                    task_data['user_id']
                )

            return result

        # Register processors for different categories
        categories = ['romantic', 'social', 'engagement', 'activity', 'retention', 'default']
        for category in categories:
            self.queue_manager.register_processor(category, send_push_notification)

        self.logger.info("Default notification processors registered")


def create_integrated_queue_system(redis_url: str = None,
                                  db_manager=None) -> NotificationQueueIntegration:
    """
    Create an integrated notification queue system

    Args:
        redis_url: Redis connection URL
        db_manager: MongoDB manager instance

    Returns:
        NotificationQueueIntegration: Integrated system
    """
    queue_manager = NotificationQueueManager(redis_url=redis_url)
    return NotificationQueueIntegration(queue_manager, db_manager)
