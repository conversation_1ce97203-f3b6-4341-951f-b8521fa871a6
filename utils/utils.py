import random
import json


def _get_value_for_placeholder(placeholder_key, predefined_values, main_names_map, name_character, city_character):
    """
    Helper to get a value for a single placeholder key.
    """
    if placeholder_key in main_names_map:
        return main_names_map[placeholder_key]
    elif placeholder_key in predefined_values:
        value_source = predefined_values[placeholder_key]
        if callable(value_source):
            return value_source()
        elif isinstance(value_source, list):
            return random.choice(value_source)
        else:
            return value_source
    # Fallback logic (can be expanded)
    elif "name" in placeholder_key.lower() and not any(x in placeholder_key for x in ["character_name", "user_name", "match_name"]): # Avoid overwriting core names here
        return name_character
    elif "city" in placeholder_key.lower():
        return city_character
    elif "body_part" in placeholder_key.lower(): # General fallback for body parts
        return random.choice(predefined_values.get("body_part_sexual", ["a body part"]))
    elif "message" in placeholder_key.lower() or "snippet" in placeholder_key.lower():
         # Prioritize suggestive if available, else generic
        if "explicit_message_snippet" in predefined_values:
            return random.choice(predefined_values["explicit_message_snippet"])
        return "a message snippet"
    elif "interest" in placeholder_key.lower():
        if "kinky_interest" in predefined_values:
            return random.choice(predefined_values["kinky_interest"])
        return "an interest"
    # SFW message fallback
    elif placeholder_key.startswith("sfw_") and placeholder_key.endswith("_message"):
        if placeholder_key in predefined_values and isinstance(predefined_values[placeholder_key], list):
            return random.choice(predefined_values[placeholder_key])
        return f"<{placeholder_key} SFW value>"
    elif placeholder_key == "photo_description":
        return f"their {random.choice(['hot selfie', 'beach body pic', 'mysterious shadow shot'])}"
    elif placeholder_key == "profile_detail":
        return f"their share about {random.choice(['their favorite book', 'a recent trip', 'their biggest dream'])}"
    else:
        return f"<{placeholder_key}_value>"


def replace_placeholders_in_string(text_string, placeholder_values):
    """
    Replaces placeholders in a single string.
    Placeholders are expected to be in the format {placeholder_key}.
    """
    processed_string = text_string
    for key, value in placeholder_values.items():
        placeholder_to_find = "{" + key + "}"
        # Ensure value is a string for replacement
        processed_string = processed_string.replace(placeholder_to_find, str(value))
    return processed_string


def process_json_object(json_obj, placeholder_values_map):
    """
    Recursively processes a JSON object (dict or list) to replace placeholders in string values.
    """
    if isinstance(json_obj, dict):
        processed_dict = {}
        for key, value in json_obj.items():
            processed_dict[key] = process_json_object(value, placeholder_values_map)
        return processed_dict
    elif isinstance(json_obj, list):
        processed_list = []
        for item in json_obj:
            processed_list.append(process_json_object(item, placeholder_values_map))
        return processed_list
    elif isinstance(json_obj, str):
        return replace_placeholders_in_string(json_obj, placeholder_values_map)
    else:
        return json_obj # For numbers, booleans, null


def generate_and_fill_notification(placeholder_data_en, notification_template_json, all_placeholder_keys, 
                                   user_name, name_character, city_character):
    """
    Generates placeholder data and fills a notification template.
    """
    # Create a map for the main names that will be consistently used
    main_names_map = {
        "character_name": name_character,
        "user_name": user_name,
        "match_name": name_character,
        "sender_name": name_character,
        "viewer_name": name_character,
        "liker_name": name_character,
        "super_liker_name": name_character,
        "potential_match_name": name_character
    }

    # Generate values for ALL placeholders found in the JSON templates
    # This ensures that even if a placeholder isn't in `placeholder_data_en` immediately,
    # it gets a fallback value.
    current_placeholder_values = {}
    for p_key in all_placeholder_keys:
        current_placeholder_values[p_key] = _get_value_for_placeholder(
            p_key,
            placeholder_data_en, # Main data source
            main_names_map,
            name_character,
            city_character
        )

    # Process the notification template (which is a list of dicts)
    processed_notifications = []
    for notification_item_template in notification_template_json:
        processed_item = process_json_object(notification_item_template, current_placeholder_values)
        processed_notifications.append(processed_item)

    return processed_notifications
def extract_keys_from_string(s, key_set):
        import re
        found = re.findall(r"\{(.*?)\}", s)
        for k in found:
            key_set.add(k)

def find_all_placeholders(obj, key_set):
    if isinstance(obj, dict):
        for k, v in obj.items():
            if isinstance(v, str):
                extract_keys_from_string(v, key_set)
            else:
                find_all_placeholders(v, key_set)
    elif isinstance(obj, list):
        for item in obj:
            find_all_placeholders(item, key_set)
    elif isinstance(obj, str):
            extract_keys_from_string(obj, key_set)
