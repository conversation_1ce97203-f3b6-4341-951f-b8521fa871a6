"""
MongoDB Database Manager for Foxy Service HM
Provides comprehensive CRUD operations and database management functionality.
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import asdict
import pymongo
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import (
    ConnectionFailure,
    ServerSelectionTimeoutError,
    DuplicateKeyError,
    OperationFailure
)
from bson import ObjectId
from bson.errors import InvalidId
import json
import time

# Import environment variables
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# Setup logging
logger = logging.getLogger(__name__)


class MongoDBManager:
    """
    MongoDB Manager class for handling all database operations
    """

    def __init__(self,
                 uri: str = None,
                 database_name: str = None,
                 username: str = None,
                 password: str = None):
        """
        Initialize MongoDB connection

        Args:
            uri: MongoDB connection URI
            database_name: Name of the database
            username: MongoDB username (optional)
            password: MongoDB password (optional)
        """
        self.uri = uri or os.getenv('MONGODB_URI', 'mongodb://localhost:27017/')
        self.database_name = database_name or os.getenv('MONGODB_DATABASE', 'partnr-chat-mgdb')
        self.username = username or os.getenv('MONGODB_USERNAME')
        self.password = password or os.getenv('MONGODB_PASSWORD')

        # Collection names from environment or defaults
        self.collections = {
            'notifications': os.getenv('COLLECTION_NOTIFICATIONS', 'notifications'),
            'chat_history': os.getenv('COLLECTION_CHAT_HISTORY', 'chat_history'),
            'users': os.getenv('COLLECTION_USERS', 'users'),
            'character_profile': "character_profile"
        }

        self.client: Optional[MongoClient] = None
        self.db: Optional[Database] = None
        self._connected = False

        # Initialize connection
        self.connect()

    def connect(self) -> bool:
        """
        Establish connection to MongoDB

        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            # Build connection string with authentication if provided
            if self.username and self.password:
                auth_uri = self.uri.replace('://', f'://{self.username}:{self.password}@')
                self.client = MongoClient(auth_uri, serverSelectionTimeoutMS=5000)
            else:
                self.client = MongoClient(self.uri, serverSelectionTimeoutMS=5000)

            # Test connection
            self.client.admin.command('ping')
            self.db = self.client[self.database_name]
            self._connected = True

            logger.info(f"Successfully connected to MongoDB database: {self.database_name}")

            return True

        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self._connected = False
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            self._connected = False
            return False

    def disconnect(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            self._connected = False
            logger.info("Disconnected from MongoDB")

    def is_connected(self) -> bool:
        """Check if connected to MongoDB"""
        return self._connected and self.client is not None

    def _create_indexes(self):
        """Create database indexes for better performance"""
        try:
            # Users collection indexes
            users_col = self.get_collection('users')
            users_col.create_index("user_id", unique=True)
            users_col.create_index("email", unique=True, sparse=True)
            users_col.create_index("created_at")
            users_col.create_index("last_active")

            # Notifications collection indexes
            notifications_col = self.get_collection('notifications')
            notifications_col.create_index("user_id")
            notifications_col.create_index("template_id")
            notifications_col.create_index("sent_at")
            notifications_col.create_index("clicked")
            notifications_col.create_index([("user_id", ASCENDING), ("sent_at", DESCENDING)])

            logger.info("Database indexes created successfully")

        except Exception as e:
            logger.error(f"Error creating indexes: {e}")

    def get_collection(self, collection_name: str) -> Collection:
        """
        Get MongoDB collection

        Args:
            collection_name: Name of the collection

        Returns:
            Collection: MongoDB collection object
        """
        if not self.is_connected():
            raise ConnectionError("Not connected to MongoDB")

        actual_name = self.collections.get(collection_name, collection_name)
        return self.db[actual_name]

    # ==================== GENERIC CRUD OPERATIONS ====================

    def insert_one(self, collection_name: str, document: Dict[str, Any]) -> Optional[str]:
        """
        Insert a single document into collection

        Args:
            collection_name: Name of the collection
            document: Document to insert

        Returns:
            str: Inserted document ID or None if failed
        """
        try:
            collection = self.get_collection(collection_name)

            # Add timestamp if not present
            if 'created_at' not in document:
                document['created_at'] = datetime.utcnow()
            if 'updated_at' not in document:
                document['updated_at'] = datetime.utcnow()

            result = collection.insert_one(document)
            logger.debug(f"Inserted document into {collection_name}: {result.inserted_id}")
            return str(result.inserted_id)

        except DuplicateKeyError as e:
            logger.error(f"Duplicate key error inserting into {collection_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error inserting document into {collection_name}: {e}")
            return None

    def insert_many(self, collection_name: str, documents: List[Dict[str, Any]]) -> List[str]:
        """
        Insert multiple documents into collection

        Args:
            collection_name: Name of the collection
            documents: List of documents to insert

        Returns:
            List[str]: List of inserted document IDs
        """
        try:
            collection = self.get_collection(collection_name)

            # Add timestamps if not present
            now = datetime.utcnow()
            for doc in documents:
                if 'created_at' not in doc:
                    doc['created_at'] = now
                if 'updated_at' not in doc:
                    doc['updated_at'] = now

            result = collection.insert_many(documents)
            logger.debug(f"Inserted {len(result.inserted_ids)} documents into {collection_name}")
            return [str(id) for id in result.inserted_ids]

        except Exception as e:
            logger.error(f"Error inserting documents into {collection_name}: {e}")
            return []

    def find_one(self, collection_name: str, filter_dict: Dict[str, Any] = None,
                 projection: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        Find a single document in collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria
            projection: Fields to include/exclude

        Returns:
            Dict: Found document or None
        """
        try:
            collection = self.get_collection(collection_name)
            filter_dict = filter_dict or {}

            result = collection.find_one(filter_dict, projection)
            if result and '_id' in result:
                result['_id'] = str(result['_id'])

            return result

        except Exception as e:
            logger.error(f"Error finding document in {collection_name}: {e}")
            return None

    def find_many(self, collection_name: str, filter_dict: Dict[str, Any] = None,
                  projection: Dict[str, Any] = None, sort: List[tuple] = None,
                  limit: int = None, skip: int = None) -> List[Dict[str, Any]]:
        """
        Find multiple documents in collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria
            projection: Fields to include/exclude
            sort: Sort criteria [(field, direction), ...]
            limit: Maximum number of documents to return
            skip: Number of documents to skip

        Returns:
            List[Dict]: List of found documents
        """
        try:
            collection = self.get_collection(collection_name)
            filter_dict = filter_dict or {}

            cursor = collection.find(filter_dict, projection)

            if sort:
                cursor = cursor.sort(sort)
            if skip:
                cursor = cursor.skip(skip)
            if limit:
                cursor = cursor.limit(limit)

            results = []
            for doc in cursor:
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                results.append(doc)

            return results

        except Exception as e:
            logger.error(f"Error finding documents in {collection_name}: {e}")
            return []

    def update_one(self, collection_name: str, filter_dict: Dict[str, Any],
                   update_dict: Dict[str, Any], upsert: bool = False) -> bool:
        """
        Update a single document in collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria
            update_dict: Update operations
            upsert: Create document if not found

        Returns:
            bool: True if update successful
        """
        try:
            collection = self.get_collection(collection_name)

            # Add updated timestamp
            if '$set' not in update_dict:
                update_dict['$set'] = {}
            update_dict['$set']['updated_at'] = datetime.utcnow()

            result = collection.update_one(filter_dict, update_dict, upsert=upsert)

            if result.modified_count > 0 or (upsert and result.upserted_id):
                logger.debug(f"Updated document in {collection_name}")
                return True
            else:
                logger.debug(f"No document updated in {collection_name}")
                return False

        except Exception as e:
            logger.error(f"Error updating document in {collection_name}: {e}")
            return False

    def update_many(self, collection_name: str, filter_dict: Dict[str, Any],
                    update_dict: Dict[str, Any]) -> int:
        """
        Update multiple documents in collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria
            update_dict: Update operations

        Returns:
            int: Number of documents updated
        """
        try:
            collection = self.get_collection(collection_name)

            # Add updated timestamp
            if '$set' not in update_dict:
                update_dict['$set'] = {}
            update_dict['$set']['updated_at'] = datetime.utcnow()

            result = collection.update_many(filter_dict, update_dict)
            logger.debug(f"Updated {result.modified_count} documents in {collection_name}")
            return result.modified_count

        except Exception as e:
            logger.error(f"Error updating documents in {collection_name}: {e}")
            return 0

    def delete_one(self, collection_name: str, filter_dict: Dict[str, Any]) -> bool:
        """
        Delete a single document from collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria

        Returns:
            bool: True if deletion successful
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.delete_one(filter_dict)

            if result.deleted_count > 0:
                logger.debug(f"Deleted document from {collection_name}")
                return True
            else:
                logger.debug(f"No document deleted from {collection_name}")
                return False

        except Exception as e:
            logger.error(f"Error deleting document from {collection_name}: {e}")
            return False

    def delete_many(self, collection_name: str, filter_dict: Dict[str, Any]) -> int:
        """
        Delete multiple documents from collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria

        Returns:
            int: Number of documents deleted
        """
        try:
            collection = self.get_collection(collection_name)
            result = collection.delete_many(filter_dict)
            logger.debug(f"Deleted {result.deleted_count} documents from {collection_name}")
            return result.deleted_count

        except Exception as e:
            logger.error(f"Error deleting documents from {collection_name}: {e}")
            return 0

    def count_documents(self, collection_name: str, filter_dict: Dict[str, Any] = None) -> int:
        """
        Count documents in collection

        Args:
            collection_name: Name of the collection
            filter_dict: Filter criteria

        Returns:
            int: Number of documents matching filter
        """
        try:
            collection = self.get_collection(collection_name)
            filter_dict = filter_dict or {}
            return collection.count_documents(filter_dict)
        except Exception as e:
            logger.error(f"Error counting documents in {collection_name}: {e}")
            return 0

    # ==================== USER MANAGEMENT ====================
    def get_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user by user_id

        Args:
            user_id: User identifier

        Returns:
            Dict: User data or None if not found
        """
        return self.find_one('users', {'user_id': user_id})
    def get_all_user(self) -> Optional[Dict[str, Any]]:
        """
        Get user by user_id

        Args:
            user_id: User identifier

        Returns:
            Dict: User data or None if not found
        """
        return self.find_many('users', {})

    # ==================== CHARACTER MANAGEMENT ====================
    def get_character_information(self, character_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user by user_id

        Args:
            user_id: User identifier

        Returns:
            Dict: User data or None if not found
        """
        return self.find_one('character_profile', {'character_id': character_id})

    # ==================== NOTIFICATION MANAGEMENT ====================

    def save_notifications(self, template_data: Dict[str, Any]) -> Optional[str]:
        """
        Save notification

        Args:
            template_data: Template data

        Returns:
            str: Template ID if successful
        """
        try:
            # Set defaults
            template_data.setdefault('num_click', 0)
            return self.insert_one('notifications', template_data)

        except Exception as e:
            logger.error(f"Error saving notification template: {e}")
            return None

    def get_notification_template(self, template_id: str) -> Optional[Dict[str, Any]]:
        """
        Get notification template by ID

        Args:
            template_id: Template identifier

        Returns:
            Dict: Template data or None if not found
        """
        return self.find_one('notification_templates', {'template_id': template_id})

    def get_all_notification(self) -> List[Dict[str, Any]]:
        """
        Get notification templates by category

        Args:
            category: Template category

        Returns:
            List[Dict]: List of templates in category
        """
        return self.find_many('notifications',{})

    def get_notification_templates_by_nsfw_level(self, max_nsfw_level: int) -> List[Dict[str, Any]]:
        """
        Get notification templates by NSFW level

        Args:
            max_nsfw_level: Maximum NSFW level allowed

        Returns:
            List[Dict]: List of suitable templates
        """
        return self.find_many('notification_templates',
                             {'nsfw_level': {'$lte': max_nsfw_level}, 'is_active': True})

    def update_notification_template(self, template_id: str, update_data: Dict[str, Any]) -> bool:
        """
        Update notification template

        Args:
            template_id: Template identifier
            update_data: Data to update

        Returns:
            bool: True if update successful
        """
        return self.update_one('notification_templates', {'template_id': template_id},
                              {'$set': update_data})

    def record_notification_sent(self, notification_data: Dict[str, Any]) -> Optional[str]:
        """
        Record that a notification was sent

        Args:
            notification_data: Notification details

        Returns:
            str: Notification record ID if successful
        """
        try:
            # Ensure required fields
            required_fields = ['user_id', 'template_id', 'title', 'content']
            for field in required_fields:
                if field not in notification_data:
                    logger.error(f"{field} is required for notification record")
                    return None

            # Add metadata
            notification_data.setdefault('sent_at', datetime.utcnow())
            notification_data.setdefault('clicked', False)
            notification_data.setdefault('click_time', None)
            notification_data.setdefault('category', 'unknown')
            notification_data.setdefault('nsfw_level', 0)
            notification_data.setdefault('confidence_score', 0.0)

            return self.insert_one('notifications', notification_data)

        except Exception as e:
            logger.error(f"Error recording notification: {e}")
            return None

    def record_notification_click(self, notification_id: str, user_id: str) -> bool:
        """
        Record that a notification was clicked

        Args:
            notification_id: Notification record ID
            user_id: User who clicked

        Returns:
            bool: True if update successful
        """
        try:
            # Convert string ID to ObjectId if needed
            if isinstance(notification_id, str) and len(notification_id) == 24:
                try:
                    notification_id = ObjectId(notification_id)
                except InvalidId:
                    pass

            update_data = {
                'clicked': True,
                'click_time': datetime.utcnow()
            }

            return self.update_one('notifications',
                                 {'_id': notification_id, 'user_id': user_id},
                                 {'$set': update_data})

        except Exception as e:
            logger.error(f"Error recording notification click: {e}")
            return False

    def get_user_notifications(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get notifications for a user

        Args:
            user_id: User identifier
            limit: Maximum number of notifications to return

        Returns:
            List[Dict]: List of user notifications
        """
        sort = [('sent_at', DESCENDING)]
        return self.find_many('notifications', {'user_id': user_id},
                             sort=sort, limit=limit)

    def get_notification_stats(self, user_id: str = None,
                              start_date: datetime = None,
                              end_date: datetime = None) -> Dict[str, Any]:
        """
        Get notification statistics

        Args:
            user_id: User identifier (optional, for user-specific stats)
            start_date: Start date for stats
            end_date: End date for stats

        Returns:
            Dict: Notification statistics
        """
        try:
            filter_dict = {}

            if user_id:
                filter_dict['user_id'] = user_id

            if start_date or end_date:
                date_filter = {}
                if start_date:
                    date_filter['$gte'] = start_date
                if end_date:
                    date_filter['$lte'] = end_date
                filter_dict['sent_at'] = date_filter

            # Get total notifications
            total_sent = self.count_documents('notifications', filter_dict)

            # Get clicked notifications
            clicked_filter = filter_dict.copy()
            clicked_filter['clicked'] = True
            total_clicked = self.count_documents('notifications', clicked_filter)

            # Calculate CTR
            ctr = (total_clicked / total_sent * 100) if total_sent > 0 else 0

            return {
                'total_sent': total_sent,
                'total_clicked': total_clicked,
                'click_through_rate': round(ctr, 2),
                'period': {
                    'start_date': start_date.isoformat() if start_date else None,
                    'end_date': end_date.isoformat() if end_date else None
                }
            }

        except Exception as e:
            logger.error(f"Error getting notification stats: {e}")
            return {
                'total_sent': 0,
                'total_clicked': 0,
                'click_through_rate': 0.0,
                'error': str(e)
            }

    # ==================== HISTORY CHAT ====================

    def get_history_chat_by_user(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Save performance metric

        Args:
            metric_data: Metric data

        Returns:
            str: Metric ID if successful
        """
        try:
            filter_dict = {'user_id': user_id}
            return self.find_many('chat_history', filter_dict)

        except Exception as e:
            logger.error(f"Error saving performance metric: {e}")
            return None

    def get_history_chat_by_user_today(self, user_id: str) -> Optional[Dict[str, Any]]:
        from datetime import datetime, timedelta
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        timestamp_start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        timestamp_end = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)

        
        filter_dict = {
            "user_id": {"$regex": f"^{user_id}"},
            "created_at": {"$gte": int(timestamp_start.timestamp()), "$lte": int(timestamp_end.timestamp())}
        }
        return self.find_many('chat_history', filter_dict)

    # ==================== UTILITY FUNCTIONS ====================

    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics

        Returns:
            Dict: Database statistics
        """
        try:
            stats = {}

            # Get collection counts
            for collection_key, collection_name in self.collections.items():
                try:
                    count = self.count_documents(collection_key)
                    stats[f"{collection_key}_count"] = count
                except Exception as e:
                    logger.error(f"Error counting {collection_key}: {e}")
                    stats[f"{collection_key}_count"] = 0

            # Get database size info
            try:
                db_stats = self.db.command("dbStats")
                stats['database_size_mb'] = round(db_stats.get('dataSize', 0) / (1024 * 1024), 2)
                stats['index_size_mb'] = round(db_stats.get('indexSize', 0) / (1024 * 1024), 2)
                stats['total_collections'] = db_stats.get('collections', 0)
            except Exception as e:
                logger.error(f"Error getting database stats: {e}")

            return stats

        except Exception as e:
            logger.error(f"Error getting database statistics: {e}")
            return {'error': str(e)}

    def cleanup_old_data(self, days_to_keep: int = 30) -> Dict[str, int]:
        """
        Clean up old data from collections

        Args:
            days_to_keep: Number of days of data to keep

        Returns:
            Dict: Number of documents deleted from each collection
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
            deleted_counts = {}

            # Clean old notifications
            deleted_counts['notifications'] = self.delete_many(
                'notifications',
                {'sent_at': {'$lt': cutoff_date}}
            )

            # Clean old performance metrics
            deleted_counts['performance_metrics'] = self.delete_many(
                'performance_metrics',
                {'date': {'$lt': cutoff_date}}
            )

            # Clean old user interactions
            deleted_counts['user_interactions'] = self.delete_many(
                'user_interactions',
                {'interaction_time': {'$lt': cutoff_date}}
            )

            logger.info(f"Cleaned up old data: {deleted_counts}")
            return deleted_counts

        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            return {'error': str(e)}

    def backup_collection(self, collection_name: str, backup_path: str) -> bool:
        """
        Backup collection to JSON file

        Args:
            collection_name: Name of collection to backup
            backup_path: Path to save backup file

        Returns:
            bool: True if backup successful
        """
        try:
            documents = self.find_many(collection_name)

            # Convert ObjectId to string for JSON serialization
            for doc in documents:
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                # Convert datetime objects to ISO format
                for key, value in doc.items():
                    if isinstance(value, datetime):
                        doc[key] = value.isoformat()

            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(documents, f, ensure_ascii=False, indent=2)

            logger.info(f"Backed up {len(documents)} documents from {collection_name} to {backup_path}")
            return True

        except Exception as e:
            logger.error(f"Error backing up collection {collection_name}: {e}")
            return False

    def restore_collection(self, collection_name: str, backup_path: str,
                          clear_existing: bool = False) -> bool:
        """
        Restore collection from JSON backup

        Args:
            collection_name: Name of collection to restore
            backup_path: Path to backup file
            clear_existing: Whether to clear existing data first

        Returns:
            bool: True if restore successful
        """
        try:
            if clear_existing:
                self.delete_many(collection_name, {})
                logger.info(f"Cleared existing data from {collection_name}")

            with open(backup_path, 'r', encoding='utf-8') as f:
                documents = json.load(f)

            # Convert ISO datetime strings back to datetime objects
            for doc in documents:
                for key, value in doc.items():
                    if isinstance(value, str) and key.endswith('_at') or key.endswith('_time'):
                        try:
                            doc[key] = datetime.fromisoformat(value.replace('Z', '+00:00'))
                        except (ValueError, AttributeError):
                            pass  # Keep as string if conversion fails

            if documents:
                inserted_ids = self.insert_many(collection_name, documents)
                logger.info(f"Restored {len(inserted_ids)} documents to {collection_name}")
                return True
            else:
                logger.warning(f"No documents found in backup file {backup_path}")
                return True

        except Exception as e:
            logger.error(f"Error restoring collection {collection_name}: {e}")
            return False

    def __enter__(self):
        """Context manager entry"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()


# ==================== CONVENIENCE FUNCTIONS ====================

def create_db_manager(uri: str = None, database_name: str = None) -> MongoDBManager:
    """
    Create a MongoDB manager instance

    Args:
        uri: MongoDB connection URI
        database_name: Database name

    Returns:
        MongoDBManager: Database manager instance
    """
    return MongoDBManager(uri=uri, database_name=database_name)


def get_default_user_data(user_id: str, **kwargs) -> Dict[str, Any]:
    """
    Get default user data structure

    Args:
        user_id: User identifier
        **kwargs: Additional user data

    Returns:
        Dict: Default user data
    """
    default_data = {
        'user_id': user_id,
        'subscription_status': 'free',
        'relationship_status': 'single',
        'gender': 'unknown',
        'age': 18,
        'gifts_given': 0,
        'diamonds_spent': 0.0,
        'num_chat_characters': 0,
        'messages_per_day': 0.0,
        'notification_click_rate': 0.0,
        'recent_chat_characters': [],
        'top_chat_characters': [],
        'online_time_periods': [],
        'last_online_time': datetime.utcnow(),
        'cumulative_clicks': 0,
        'cumulative_notifications': 0,
        'recent_activity_level': 0.0,
        'preferred_character_types': [],
        'current_mood': 'neutral',
        'is_active': True
    }

    # Update with provided kwargs
    default_data.update(kwargs)
    return default_data


def get_default_preferences(user_id: str, **kwargs) -> Dict[str, Any]:
    """
    Get default user preferences structure

    Args:
        user_id: User identifier
        **kwargs: Additional preference data

    Returns:
        Dict: Default preferences
    """
    default_prefs = {
        'user_id': user_id,
        'preferred_hours': list(range(9, 22)),  # 9 AM to 9 PM
        'preferred_days': list(range(7)),  # All days
        'max_notifications_per_day': 3,
        'time_zone_offset': 0,
        'do_not_disturb_start': 23,
        'do_not_disturb_end': 7,
        'notification_frequency': 'medium',
        'nsfw_tolerance': 1,
        'preferred_categories': [],
        'blocked_categories': []
    }

    # Update with provided kwargs
    default_prefs.update(kwargs)
    return default_prefs