# Redis Configuration
# For local Docker development
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_USERNAME=default
REDIS_PASSWORD=your_redis_password_here

# For external Redis service (production)
# REDIS_HOST=your-external-redis-host
# REDIS_PORT=19926
# REDIS_USERNAME=default
# REDIS_PASSWORD=your-external-redis-password

# MongoDB Configuration
# For local Docker development
MONGODB_URI=*******************************************************/
MONGODB_DATABASE=foxy_ai
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your_secure_password_here

# For external MongoDB service (production)
# MONGODB_URI=************************************************************/
# MONGODB_DATABASE=foxy_ai
# MONGODB_USERNAME=your_username
# MONGODB_PASSWORD=your_password

# MongoDB Collections
COLLECTION_USERS=users
COLLECTION_NOTIFICATIONS=notifications
COLLECTION_CHAT_HISTORY=chat_history

# Optional: Web UI Credentials (for development tools)
MONGO_EXPRESS_USERNAME=admin
MONGO_EXPRESS_PASSWORD=your_mongo_express_password

# Application Settings
DEBUG=True
LOG_LEVEL=INFO
PYTHONPATH=/app

