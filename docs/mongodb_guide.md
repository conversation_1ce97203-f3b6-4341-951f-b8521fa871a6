# MongoDB Database Manager Guide

Hướng dẫn sử dụng MongoDB Database Manager cho Foxy Service HM.

## Mụ<PERSON> lục

1. [<PERSON><PERSON><PERSON> đặt](#cài-đặt)
2. [<PERSON><PERSON><PERSON> hình](#cấu-hình)
3. [Kết nối Database](#kết-nối-database)
4. [Quản lý Users](#quản-lý-users)
5. [Quản lý Notifications](#quản-lý-notifications)
6. [Performance Metrics](#performance-metrics)
7. [Utility Functions](#utility-functions)
8. [Examples](#examples)

## Cài đặt

### 1. Cài đặt dependencies

```bash
pip install pymongo python-dotenv bson
```

### 2. Cấu hình MongoDB

Tạo file `.env` từ `.env.example`:

```bash
cp .env.example .env
```

Chỉnh sửa file `.env`:

```env
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/
MONGODB_DATABASE=foxy_service_hm
MONGODB_USERNAME=your_username
MONGODB_PASSWORD=your_password

# MongoDB Collections
COLLECTION_USERS=users
COLLECTION_NOTIFICATIONS=notifications
COLLECTION_USER_PREFERENCES=user_preferences
COLLECTION_PERFORMANCE_METRICS=performance_metrics
COLLECTION_NOTIFICATION_TEMPLATES=notification_templates
COLLECTION_USER_INTERACTIONS=user_interactions
```

## Cấu hình

### Kết nối cơ bản

```python
from utils.mgdatabase import MongoDBManager, create_db_manager

# Cách 1: Sử dụng environment variables
db = MongoDBManager()

# Cách 2: Truyền tham số trực tiếp
db = MongoDBManager(
    uri="mongodb://localhost:27017/",
    database_name="foxy_service_hm"
)

# Cách 3: Sử dụng convenience function
db = create_db_manager()
```

### Context Manager

```python
# Tự động đóng kết nối khi hoàn thành
with MongoDBManager() as db:
    # Thực hiện các operations
    user = db.get_user("user_001")
    print(user)
# Kết nối tự động đóng
```

## Quản lý Users

### Tạo User mới

```python
from utils.mgdatabase import get_default_user_data

# Tạo user data với defaults
user_data = get_default_user_data(
    user_id="user_001",
    email="<EMAIL>",
    age=25,
    gender="female",
    subscription_status="premium"
)

# Lưu vào database
user_id = db.create_user(user_data)
if user_id:
    print(f"Created user: {user_id}")
```

### Lấy thông tin User

```python
# Lấy user theo user_id
user = db.get_user("user_001")

# Lấy user theo email
user = db.get_user_by_email("<EMAIL>")

# Lấy danh sách users active
active_users = db.get_active_users(limit=10)

# Lấy users theo subscription
premium_users = db.get_users_by_subscription("premium")
```

### Cập nhật User

```python
# Cập nhật thông tin cơ bản
success = db.update_user("user_001", {
    "age": 26,
    "current_mood": "happy",
    "recent_activity_level": 0.8
})

# Cập nhật activity
success = db.update_user_activity("user_001", {
    "recent_activity_level": 0.9,
    "current_mood": "excited"
})

# Deactivate user
success = db.deactivate_user("user_001")
```

### Quản lý User Preferences

```python
from utils.mgdatabase import get_default_preferences

# Tạo preferences
preferences = get_default_preferences(
    user_id="user_001",
    preferred_hours=[9, 10, 11, 18, 19, 20],
    nsfw_tolerance=2,
    preferred_categories=["romantic", "social"],
    blocked_categories=["nsfw"]
)

# Lưu preferences
pref_id = db.create_user_preferences("user_001", preferences)

# Lấy preferences
prefs = db.get_user_preferences("user_001")

# Cập nhật preferences
success = db.update_user_preferences("user_001", {
    "nsfw_tolerance": 3,
    "preferred_categories": ["romantic", "social", "activity"]
})
```

## Quản lý Notifications

### Lưu Notification Template

```python
template_data = {
    "template_id": "welcome_001",
    "title": "Welcome to Foxy Service! 🎉",
    "content": "Hi {user_name}, welcome to our amazing dating app!",
    "notification_type": "Welcome",
    "category": "engagement",
    "nsfw_level": 0,
    "engagement_score": 0.8,
    "placeholder_variables": ["user_name"]
}

template_id = db.save_notification_template(template_data)
```

### Lấy Templates

```python
# Lấy template theo ID
template = db.get_notification_template("welcome_001")

# Lấy templates theo category
templates = db.get_notification_templates_by_category("romantic")

# Lấy templates theo NSFW level
safe_templates = db.get_notification_templates_by_nsfw_level(1)
```

### Ghi nhận Notification đã gửi

```python
notification_data = {
    "user_id": "user_001",
    "template_id": "welcome_001",
    "title": "Welcome to Foxy Service! 🎉",
    "content": "Hi John, welcome to our amazing dating app!",
    "category": "engagement",
    "nsfw_level": 0,
    "confidence_score": 0.85
}

notif_id = db.record_notification_sent(notification_data)
```

### Ghi nhận Click

```python
# Ghi nhận user đã click notification
success = db.record_notification_click(notif_id, "user_001")
```

### Thống kê Notifications

```python
# Thống kê tổng quát
stats = db.get_notification_stats()

# Thống kê theo user
user_stats = db.get_notification_stats(user_id="user_001")

# Thống kê theo thời gian
from datetime import datetime, timedelta
start_date = datetime.utcnow() - timedelta(days=7)
weekly_stats = db.get_notification_stats(start_date=start_date)

print(f"CTR: {stats['click_through_rate']}%")
```

## Performance Metrics

### Lưu Metrics

```python
metric_data = {
    "metric_type": "category_performance",
    "category": "romantic",
    "total_sent": 100,
    "total_clicked": 25,
    "click_through_rate": 25.0
}

metric_id = db.save_performance_metric(metric_data)
```

### Lấy Metrics

```python
# Lấy metrics theo type
metrics = db.get_performance_metrics(metric_type="category_performance")

# Lấy metrics theo thời gian
from datetime import datetime, timedelta
start_date = datetime.utcnow() - timedelta(days=30)
monthly_metrics = db.get_performance_metrics(start_date=start_date)

# Lấy performance theo category
category_perf = db.get_category_performance(category="romantic")
```

### User Interactions

```python
# Ghi nhận interaction
interaction_data = {
    "user_id": "user_001",
    "interaction_type": "notification_click",
    "notification_id": "notif_123",
    "details": {
        "category": "romantic",
        "template_id": "romantic_001"
    }
}

interaction_id = db.record_user_interaction(interaction_data)

# Lấy interactions của user
interactions = db.get_user_interactions("user_001", limit=50)
```

## Utility Functions

### Database Statistics

```python
# Lấy thống kê database
stats = db.get_database_stats()
print(f"Total users: {stats['users_count']}")
print(f"Database size: {stats['database_size_mb']} MB")
```

### Backup & Restore

```python
# Backup collection
success = db.backup_collection("users", "backup_users.json")

# Restore collection
success = db.restore_collection("users", "backup_users.json", clear_existing=True)
```

### Cleanup Old Data

```python
# Xóa data cũ hơn 30 ngày
deleted_counts = db.cleanup_old_data(days_to_keep=30)
print(f"Deleted {deleted_counts['notifications']} old notifications")
```

## Examples

Xem file `examples/mongodb_usage_examples.py` để có ví dụ chi tiết về cách sử dụng tất cả các chức năng.

```bash
python examples/mongodb_usage_examples.py
```

## Error Handling

```python
try:
    with MongoDBManager() as db:
        if not db.is_connected():
            print("Failed to connect to MongoDB")
            return
        
        # Thực hiện operations
        user = db.get_user("user_001")
        
except Exception as e:
    print(f"Database error: {e}")
```

## Best Practices

1. **Sử dụng Context Manager**: Luôn sử dụng `with` statement để tự động đóng kết nối
2. **Error Handling**: Luôn kiểm tra kết nối và xử lý exceptions
3. **Indexing**: Database tự động tạo indexes cho performance tốt hơn
4. **Backup**: Thường xuyên backup dữ liệu quan trọng
5. **Cleanup**: Định kỳ xóa dữ liệu cũ để tiết kiệm storage

## Troubleshooting

### Lỗi kết nối
- Kiểm tra MongoDB service đang chạy
- Kiểm tra URI và credentials trong `.env`
- Kiểm tra network connectivity

### Lỗi permissions
- Đảm bảo user có quyền read/write database
- Kiểm tra authentication settings

### Performance issues
- Sử dụng indexes phù hợp
- Limit số lượng documents khi query
- Cleanup dữ liệu cũ thường xuyên
