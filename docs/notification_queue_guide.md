# Notification Queue Guide - BullMQ Integration

Hướng dẫn sử dụng Notification Queue với BullMQ cho Foxy Service HM.

## <PERSON><PERSON><PERSON> l<PERSON>

1. [Tổng quan](#tổng-quan)
2. [<PERSON><PERSON><PERSON> đặt](#cài-đặt)
3. [<PERSON><PERSON><PERSON> hình](#c<PERSON><PERSON>-hình)
4. [Sử dụng cơ bản](#sử-dụng-cơ-bản)
5. [Tích hợp với hệ thống](#tích-hợp-với-hệ-thống)
6. [Queue Management](#queue-management)
7. [Monitoring & Stats](#monitoring--stats)
8. [Best Practices](#best-practices)

## Tổng quan

Notification Queue System sử dụng BullMQ để quản lý việc gửi thông báo một cách bất đồng bộ, có thể mở rộng và đáng tin cậy.

### Tính năng chính:
- ✅ **Asynchronous Processing**: Xử lý thông báo không đồng bộ
- ✅ **Priority Queue**: Ưu tiên thông báo quan trọng
- ✅ **Delayed/Scheduled Jobs**: <PERSON><PERSON><PERSON> lịch gửi thông báo
- ✅ **Retry Logic**: Tự động retry khi thất bại
- ✅ **Bulk Operations**: Xử lý hàng loạt thông báo
- ✅ **Monitoring**: Theo dõi queue stats và performance
- ✅ **Integration**: Tích hợp với MongoDB và Notification System

## Cài đặt

### 1. Dependencies

```bash
pip install bullmq redis
```

### 2. Redis Setup

BullMQ yêu cầu Redis server:

```bash
# Install Redis (macOS)
brew install redis
redis-server

# Install Redis (Ubuntu)
sudo apt install redis-server
sudo systemctl start redis
```

## Cấu hình

### Environment Variables

Thêm vào file `.env`:

```env
# Redis Configuration for BullMQ
REDIS_URL=redis://localhost:6379

# For Redis with authentication:
# REDIS_URL=redis://username:password@hostname:port

# For Redis Cloud/Remote:
# REDIS_URL=redis://default:<EMAIL>:6379

# Example with the provided Redis URL:
REDIS_URL=redis://default:<EMAIL>:19926
```

### Kiểm tra kết nối Redis

Trước khi sử dụng, hãy kiểm tra kết nối Redis:

```bash
# Test Redis connection
python examples/test_redis_connection.py
```

### Basic Setup

```python
import os
from utils.notification_queue import NotificationQueueManager, create_notification_task

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Initialize queue manager with environment REDIS_URL
queue_manager = NotificationQueueManager(
    redis_url=os.getenv('REDIS_URL', 'redis://localhost:6379'),
    queue_name="notifications",
    max_concurrency=10
)

# Or use the convenience function
from utils.notification_queue import create_queue_manager
queue_manager = await create_queue_manager(
    redis_url=os.getenv('REDIS_URL'),
    queue_name="notifications"
)
```

## Sử dụng cơ bản

### 1. Tạo Notification Task

#### **Basic Task:**
```python
from utils.notification_queue import create_notification_task

# Tạo task đơn giản (gửi ngay lập tức)
task = create_notification_task(
    notification_id="notif_001",
    user_id="user_123",
    title="🎉 Welcome!",
    content="Welcome to our app!",
    category="welcome",
    priority=5
)
```

#### **Scheduled Task (Ngày và giờ cụ thể):**
```python
from utils.notification_queue import create_scheduled_task

# Gửi vào ngày cụ thể lúc 9:30 AM
task = create_scheduled_task(
    notification_id="scheduled_001",
    user_id="user_123",
    title="🌅 Good morning!",
    content="Start your day with new matches!",
    category="morning",
    send_on_date="2024-12-25",  # YYYY-MM-DD
    send_at_hour=9,             # 0-23
    send_at_minute=30,          # 0-59
    priority=5
)
```

#### **Delayed Task (Thời gian tương đối):**
```python
from utils.notification_queue import create_delayed_task

# Gửi sau 2 giờ
task = create_delayed_task(
    notification_id="delayed_001",
    user_id="user_123",
    title="⏰ Reminder",
    content="Don't forget to check your messages!",
    category="reminder",
    send_after_hours=2,
    priority=3
)

# Gửi sau 30 phút
task = create_delayed_task(
    notification_id="delayed_002",
    user_id="user_456",
    title="📱 Quick follow-up",
    content="Complete your profile for better matches!",
    category="engagement",
    send_after_minutes=30,
    priority=4
)

# Gửi sau 3 ngày
task = create_delayed_task(
    notification_id="delayed_003",
    user_id="user_789",
    title="📅 We miss you!",
    content="Come back and see what's new!",
    category="retention",
    send_after_days=3,
    priority=2
)
```

#### **Time-based Task (Giờ cụ thể hôm nay/ngày mai):**
```python
# Gửi lúc 6:00 PM hôm nay (hoặc ngày mai nếu đã qua giờ)
task = create_notification_task(
    notification_id="time_001",
    user_id="user_123",
    title="🌆 Evening time!",
    content="Perfect time for conversations!",
    category="social",
    send_at_hour=18,    # 6 PM
    send_at_minute=0,
    priority=4
)
```

#### **Recurring Task (Lặp lại):**
```python
from utils.notification_queue import create_recurring_task

# Gửi hàng ngày lúc 9:00 AM
task = create_recurring_task(
    notification_id="daily_reminder",
    user_id="user_123",
    title="🌅 Daily motivation",
    content="Start your day with positive energy!",
    category="daily",
    recurring_interval="daily",
    send_at_hour=9,
    send_at_minute=0,
    priority=3
)
```

### 2. Thêm Task vào Queue

```python
# Thêm single task
job_id = await queue_manager.add_notification_task(task)
print(f"Job added: {job_id}")

# Thêm multiple tasks
tasks = [task1, task2, task3]
job_ids = await queue_manager.add_bulk_notification_tasks(tasks)
print(f"Added {len(job_ids)} jobs")
```

### 3. Đăng ký Processors

```python
async def send_romantic_notification(task_data: dict) -> dict:
    """Process romantic notifications"""
    print(f"💕 Sending romantic notification to {task_data['user_id']}")

    # Integrate with push notification service
    # await send_push_notification(task_data)

    return {
        'status': 'sent',
        'channel': 'push',
        'sent_at': datetime.utcnow().isoformat()
    }

# Register processor
queue_manager.register_processor('romantic', send_romantic_notification)
```

### 4. Start Worker

```python
# Start processing
await queue_manager.start_worker()

# Stop processing
await queue_manager.stop_worker()
```

## Tích hợp với hệ thống

### 1. Integration với Notification System

```python
from utils.notification_queue import NotificationQueueIntegration
from utils.mgdatabase import MongoDBManager

# Setup integrated system
db_manager = MongoDBManager()
queue_integration = NotificationQueueIntegration(queue_manager, db_manager)

# Setup default processors
await queue_integration.setup_default_processors()

# Queue recommendation from notification system
recommendation = notification_system.get_recommendation(user_context)
job_id = await queue_integration.queue_recommendation(recommendation, user_context)
```

### 2. Complete Service Integration

```python
from examples.complete_integration_demo import CompleteNotificationService

# Initialize complete service
service = CompleteNotificationService(
    notification_json_path="notification.json",
    redis_url="redis://localhost:6379",
    mongodb_uri="mongodb://localhost:27017/"
)

# Create user and queue recommendation
await service.create_and_save_user("user_001", age=25, gender="female")
job_id = await service.get_and_queue_recommendation("user_001")

# Process notifications
await service.process_notifications(duration_seconds=30)
```

## Queue Management

### 1. Queue Statistics

```python
# Get queue stats
stats = await queue_manager.get_queue_stats()
print(f"Waiting: {stats['waiting']}")
print(f"Active: {stats['active']}")
print(f"Completed: {stats['completed']}")
print(f"Failed: {stats['failed']}")
```

### 2. Job Management

```python
# Get jobs by status
waiting_jobs = await queue_manager.get_jobs('waiting', limit=10)
failed_jobs = await queue_manager.get_jobs('failed', limit=5)

# Retry failed jobs
retried_count = await queue_manager.retry_failed_jobs(limit=10)
print(f"Retried {retried_count} jobs")
```

### 3. Queue Control

```python
# Pause queue
await queue_manager.pause_queue()

# Resume queue
await queue_manager.resume_queue()

# Clean old jobs
cleaned = await queue_manager.clean_old_jobs(grace_period_hours=24)
print(f"Cleaned {cleaned['total']} old jobs")
```

## Monitoring & Stats

### 1. Real-time Monitoring

```python
async def monitor_queue():
    while True:
        stats = await queue_manager.get_queue_stats()
        print(f"Queue Status: {stats}")
        await asyncio.sleep(5)

# Start monitoring
asyncio.create_task(monitor_queue())
```

### 2. Performance Metrics

```python
# Get comprehensive stats
service_stats = await service.get_service_stats()
print(f"Database: {service_stats['database']}")
print(f"Queue: {service_stats['queue']}")
print(f"Notifications: {service_stats['notifications']}")
```

## Advanced Scheduling Options

### 1. Scheduling Priority Order

Hệ thống sẽ ưu tiên các tùy chọn thời gian theo thứ tự sau:
1. **`scheduled_time`** - Thời gian cụ thể (datetime object)
2. **`send_on_date` + `send_at_hour/minute`** - Ngày và giờ cụ thể
3. **`send_at_hour/minute`** - Giờ cụ thể hôm nay/ngày mai
4. **`send_after_days/hours/minutes`** - Thời gian tương đối
5. **`delay`** - Độ trễ tính bằng giây

### 2. Scheduled Notifications (Ngày giờ cụ thể)

```python
from utils.notification_queue import create_scheduled_task

# Gửi vào Giáng sinh lúc 9:00 AM
christmas_task = create_scheduled_task(
    notification_id="christmas_2024",
    user_id="user_123",
    title="🎄 Merry Christmas!",
    content="Wishing you a wonderful Christmas day!",
    category="holiday",
    send_on_date="2024-12-25",
    send_at_hour=9,
    send_at_minute=0,
    priority=8
)

# Gửi vào cuối tuần lúc 7:30 PM
weekend_task = create_scheduled_task(
    notification_id="weekend_reminder",
    user_id="user_456",
    title="🍻 Weekend vibes!",
    content="Perfect time for weekend conversations!",
    category="social",
    send_on_date="2024-12-21",  # Saturday
    send_at_hour=19,
    send_at_minute=30,
    priority=5
)
```

### 3. Delayed Notifications (Thời gian tương đối)

```python
from utils.notification_queue import create_delayed_task

# Onboarding sequence
welcome_series = [
    # Ngay sau khi đăng ký
    create_notification_task(
        notification_id="welcome_immediate",
        user_id="new_user_123",
        title="🎉 Welcome aboard!",
        content="Thanks for joining our community!",
        category="onboarding",
        priority=10
    ),

    # Sau 30 phút
    create_delayed_task(
        notification_id="welcome_30min",
        user_id="new_user_123",
        title="📱 Quick tour",
        content="Let's take a quick tour of the app!",
        category="onboarding",
        send_after_minutes=30,
        priority=8
    ),

    # Sau 2 giờ
    create_delayed_task(
        notification_id="welcome_2hour",
        user_id="new_user_123",
        title="💬 Start chatting",
        content="Ready to start your first conversation?",
        category="onboarding",
        send_after_hours=2,
        priority=6
    ),

    # Sau 1 ngày
    create_delayed_task(
        notification_id="welcome_1day",
        user_id="new_user_123",
        title="📊 Profile tips",
        content="Here are some tips to improve your profile!",
        category="onboarding",
        send_after_days=1,
        priority=4
    )
]
```

### 4. Time-based Notifications (Giờ cụ thể)

```python
# Gửi lúc 9:00 AM hôm nay (hoặc ngày mai nếu đã qua giờ)
morning_motivation = create_notification_task(
    notification_id="morning_boost",
    user_id="user_123",
    title="🌅 Good morning!",
    content="Start your day with positive energy!",
    category="motivation",
    send_at_hour=9,
    send_at_minute=0,
    priority=5
)

# Gửi lúc 6:00 PM (prime time)
evening_reminder = create_notification_task(
    notification_id="evening_activity",
    user_id="user_456",
    title="🌆 Prime time!",
    content="Most people are online now. Perfect for chatting!",
    category="engagement",
    send_at_hour=18,
    send_at_minute=0,
    priority=7
)

# Gửi lúc 10:30 PM (night owls)
night_special = create_notification_task(
    notification_id="night_owl",
    user_id="user_789",
    title="🌙 Night conversations",
    content="Late night chats can be the most meaningful!",
    category="social",
    send_at_hour=22,
    send_at_minute=30,
    priority=3
)
```

### 5. Recurring Notifications

```python
from utils.notification_queue import create_recurring_task

# Daily motivation (9:00 AM mỗi ngày)
daily_motivation = create_recurring_task(
    notification_id="daily_motivation",
    user_id="user_123",
    title="🌅 Daily inspiration",
    content="Today is a new opportunity to connect!",
    category="daily",
    recurring_interval="daily",
    send_at_hour=9,
    send_at_minute=0,
    priority=4
)

# Weekly summary (Sunday 8:00 PM)
weekly_summary = create_recurring_task(
    notification_id="weekly_summary",
    user_id="user_456",
    title="📊 Weekly recap",
    content="Here's your week in review!",
    category="weekly",
    recurring_interval="weekly",
    send_at_hour=20,
    send_at_minute=0,
    priority=3
)
```

### 6. Mixed Scheduling Examples

```python
# Ưu tiên cao: gửi ngay lập tức
urgent_task = create_notification_task(
    notification_id="security_alert",
    user_id="user_123",
    title="🚨 Security Alert",
    content="Unusual login detected. Please verify.",
    category="security",
    priority=10  # Highest priority
)

# Trung bình: gửi sau 1 giờ
reminder_task = create_delayed_task(
    notification_id="profile_reminder",
    user_id="user_456",
    title="📝 Complete your profile",
    content="Add more photos for better matches!",
    category="engagement",
    send_after_hours=1,
    priority=5
)

# Thấp: gửi vào ngày cụ thể
event_task = create_scheduled_task(
    notification_id="valentine_2024",
    user_id="user_789",
    title="💕 Valentine's Day",
    content="Find your special someone this Valentine's!",
    category="event",
    send_on_date="2024-02-14",
    send_at_hour=10,
    send_at_minute=0,
    priority=2
)
```

## Error Handling

### 1. Retry Configuration

```python
task = create_notification_task(
    notification_id="retry_test",
    user_id="user_123",
    title="Test notification",
    content="This will retry on failure",
    category="test",
    retry_attempts=5  # Retry up to 5 times
)
```

### 2. Error Processing

```python
async def robust_processor(task_data: dict) -> dict:
    try:
        # Process notification
        result = await send_notification(task_data)
        return result
    except Exception as e:
        logger.error(f"Error processing notification: {e}")
        # Re-raise to trigger retry
        raise
```

## Best Practices

### 1. Queue Design
- Sử dụng priority cho thông báo quan trọng
- Batch processing cho hiệu suất tốt hơn
- Set appropriate retry limits
- Monitor queue health thường xuyên

### 2. Performance
- Adjust concurrency dựa trên server capacity
- Use bulk operations khi có thể
- Clean old jobs định kỳ
- Monitor Redis memory usage

### 3. Error Handling
- Implement proper error logging
- Set reasonable retry attempts
- Handle failed jobs appropriately
- Monitor failed job rates

### 4. Monitoring
- Track queue metrics
- Set up alerts cho queue backup
- Monitor processing times
- Track success/failure rates

## Examples

### Chạy Examples

```bash
# Test Redis connection
python examples/test_redis_connection.py

# Basic queue operations
python examples/notification_queue_examples.py

# Advanced scheduling examples
python examples/advanced_scheduling_examples.py

# Complete integration demo
python examples/complete_integration_demo.py
```

### Advanced Scheduling Examples

File `examples/advanced_scheduling_examples.py` minh họa tất cả các tùy chọn thời gian:

#### **1. Immediate Notifications:**
- Gửi ngay lập tức với priority cao
- Xử lý trong vài giây

#### **2. Scheduled Notifications:**
- Gửi vào ngày và giờ cụ thể
- Ví dụ: ngày mai lúc 9:00 AM, 2:30 PM, etc.

#### **3. Delayed Notifications:**
- Gửi sau X phút/giờ/ngày
- Ví dụ: sau 2 phút, 1 giờ, 3 ngày

#### **4. Time-based Notifications:**
- Gửi vào giờ cụ thể hôm nay/ngày mai
- Tự động schedule cho ngày mai nếu đã qua giờ

#### **5. Mixed Scheduling:**
- Kết hợp nhiều loại scheduling
- Ưu tiên theo priority và timing

### Custom Processor Example

```python
async def custom_email_processor(task_data: dict) -> dict:
    """Custom email notification processor"""

    # Extract data
    user_id = task_data['user_id']
    title = task_data['title']
    content = task_data['content']

    # Send email (integrate with your email service)
    email_result = await send_email(
        to=get_user_email(user_id),
        subject=title,
        body=content
    )

    # Log to database
    if db_manager:
        db_manager.record_user_interaction({
            'user_id': user_id,
            'interaction_type': 'email_sent',
            'notification_id': task_data['notification_id'],
            'success': email_result['success']
        })

    return {
        'status': 'sent' if email_result['success'] else 'failed',
        'channel': 'email',
        'message_id': email_result.get('message_id'),
        'sent_at': datetime.utcnow().isoformat()
    }

# Register custom processor
queue_manager.register_processor('email', custom_email_processor)
```

## Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   - Check Redis server is running
   - Verify REDIS_URL in environment
   - Check network connectivity

2. **Jobs Stuck in Queue**
   - Check worker is running
   - Verify processor is registered
   - Check for errors in processor

3. **High Memory Usage**
   - Clean old completed jobs
   - Reduce job retention time
   - Monitor Redis memory

4. **Slow Processing**
   - Increase worker concurrency
   - Optimize processor functions
   - Check Redis performance

### Debug Commands

```python
# Check queue health
stats = await queue_manager.get_queue_stats()
print(f"Queue health: {stats}")

# Check failed jobs
failed_jobs = await queue_manager.get_jobs('failed')
for job in failed_jobs:
    print(f"Failed job: {job['id']} - {job.get('failed_reason')}")

# Retry all failed jobs
retried = await queue_manager.retry_failed_jobs()
print(f"Retried {retried} jobs")
```
