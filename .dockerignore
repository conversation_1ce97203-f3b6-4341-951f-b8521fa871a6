# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
.dockerignore
docker-compose*.yml

# Documentation
README.md
*.md
docs/

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Testing
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Logs
*.log
logs/
log/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Local data
data/
datasets/
*.csv
*.json
*.xml
*.xlsx
*.xls

# Model files
models/
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5

# Backup files
*.bak
*.backup
*.old

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
cache/
.cache/

# Configuration files with sensitive data
config/local.py
config/production.py
secrets.json
credentials.json

# Project specific
notification_logs/
user_data/
analytics/
performance_reports/
