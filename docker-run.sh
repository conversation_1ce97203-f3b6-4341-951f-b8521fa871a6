#!/bin/bash

# Foxy AI Service Docker Management Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f .env ]; then
        print_warning ".env file not found. Creating from .env.example..."
        if [ -f .env.example ]; then
            cp .env.example .env
            print_warning "Please edit .env file with your actual configuration values"
            return 1
        else
            print_error ".env.example file not found. Please create .env file manually."
            exit 1
        fi
    fi
    return 0
}

# Function to build the Docker image
build() {
    print_status "Building Foxy AI Service Docker image..."
    docker build -t foxy-ai-service:latest .
    print_success "Docker image built successfully!"
}

# Function to start services
start() {
    check_env_file
    if [ $? -eq 1 ]; then
        print_error "Please configure .env file before starting services"
        exit 1
    fi
    
    print_status "Starting Foxy AI Service and dependencies..."
    docker-compose up -d
    print_success "Services started successfully!"
    print_status "Application logs: docker-compose logs -f foxy-ai-service"
    print_status "MongoDB UI: http://localhost:8082 (if tools profile enabled)"
    print_status "Redis UI: http://localhost:8081 (if tools profile enabled)"
}

# Function to start with development tools
start_with_tools() {
    check_env_file
    if [ $? -eq 1 ]; then
        print_error "Please configure .env file before starting services"
        exit 1
    fi
    
    print_status "Starting Foxy AI Service with development tools..."
    docker-compose --profile tools up -d
    print_success "Services started with development tools!"
    print_status "Application: docker-compose logs -f foxy-ai-service"
    print_status "MongoDB UI: http://localhost:8082"
    print_status "Redis UI: http://localhost:8081"
}

# Function to stop services
stop() {
    print_status "Stopping Foxy AI Service..."
    docker-compose down
    print_success "Services stopped successfully!"
}

# Function to restart services
restart() {
    print_status "Restarting Foxy AI Service..."
    docker-compose restart
    print_success "Services restarted successfully!"
}

# Function to view logs
logs() {
    if [ -z "$2" ]; then
        docker-compose logs -f
    else
        docker-compose logs -f "$2"
    fi
}

# Function to clean up
clean() {
    print_status "Cleaning up Docker resources..."
    docker-compose down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup completed!"
}

# Function to show status
status() {
    print_status "Service status:"
    docker-compose ps
}

# Function to show help
show_help() {
    echo "Foxy AI Service Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  build           Build the Docker image"
    echo "  start           Start services (app, mongodb, redis)"
    echo "  start-tools     Start services with development tools (mongo-express, redis-commander)"
    echo "  stop            Stop all services"
    echo "  restart         Restart all services"
    echo "  logs [service]  View logs (optionally for specific service)"
    echo "  status          Show service status"
    echo "  clean           Stop services and clean up volumes"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 start"
    echo "  $0 logs foxy-ai-service"
    echo "  $0 start-tools"
}

# Main script logic
case "$1" in
    build)
        build
        ;;
    start)
        start
        ;;
    start-tools)
        start_with_tools
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    logs)
        logs "$@"
        ;;
    status)
        status
        ;;
    clean)
        clean
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
